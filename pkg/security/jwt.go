package security

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// JWTClaims represents the JWT claims structure
type JWTClaims struct {
	UserID      string   `json:"user_id"`
	Username    string   `json:"username"`
	Email       string   `json:"email"`
	Roles       []string `json:"roles"`
	Permissions []string `json:"permissions"`
	SessionID   string   `json:"session_id"`
	jwt.RegisteredClaims
}

// JWTManager handles JWT token operations
type JWTManager struct {
	secretKey     string
	issuer        string
	accessTTL     time.Duration
	refreshTTL    time.Duration
	signingMethod jwt.SigningMethod
}

// NewJWTManager creates a new JWT manager
func NewJWTManager(secretKey, issuer string, accessTTL, refreshTTL time.Duration) *JWTManager {
	return &JWTManager{
		secretKey:     secretKey,
		issuer:        issuer,
		accessTTL:     accessTTL,
		refreshTTL:    refreshTTL,
		signingMethod: jwt.SigningMethodHS256,
	}
}

// TokenPair represents access and refresh tokens
type TokenPair struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	TokenType    string    `json:"token_type"`
	ExpiresIn    int64     `json:"expires_in"`
	ExpiresAt    time.Time `json:"expires_at"`
}

// UserInfo represents user information for token generation
type UserInfo struct {
	UserID      string
	Username    string
	Email       string
	Roles       []string
	Permissions []string
	SessionID   string
}

// GenerateTokenPair generates access and refresh tokens
func (jm *JWTManager) GenerateTokenPair(userInfo UserInfo) (*TokenPair, error) {
	now := time.Now()
	
	// Generate access token
	accessToken, err := jm.generateAccessToken(userInfo, now)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}
	
	// Generate refresh token
	refreshToken, err := jm.generateRefreshToken(userInfo, now)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}
	
	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    int64(jm.accessTTL.Seconds()),
		ExpiresAt:    now.Add(jm.accessTTL),
	}, nil
}

// generateAccessToken generates an access token
func (jm *JWTManager) generateAccessToken(userInfo UserInfo, now time.Time) (string, error) {
	claims := JWTClaims{
		UserID:      userInfo.UserID,
		Username:    userInfo.Username,
		Email:       userInfo.Email,
		Roles:       userInfo.Roles,
		Permissions: userInfo.Permissions,
		SessionID:   userInfo.SessionID,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    jm.issuer,
			Subject:   userInfo.UserID,
			Audience:  []string{"key-model-tracking-api"},
			ExpiresAt: jwt.NewNumericDate(now.Add(jm.accessTTL)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
			ID:        userInfo.SessionID,
		},
	}
	
	token := jwt.NewWithClaims(jm.signingMethod, claims)
	return token.SignedString([]byte(jm.secretKey))
}

// generateRefreshToken generates a refresh token
func (jm *JWTManager) generateRefreshToken(userInfo UserInfo, now time.Time) (string, error) {
	claims := jwt.RegisteredClaims{
		Issuer:    jm.issuer,
		Subject:   userInfo.UserID,
		Audience:  []string{"key-model-tracking-api-refresh"},
		ExpiresAt: jwt.NewNumericDate(now.Add(jm.refreshTTL)),
		NotBefore: jwt.NewNumericDate(now),
		IssuedAt:  jwt.NewNumericDate(now),
		ID:        userInfo.SessionID,
	}
	
	token := jwt.NewWithClaims(jm.signingMethod, claims)
	return token.SignedString([]byte(jm.secretKey))
}

// ValidateToken validates and parses a JWT token
func (jm *JWTManager) ValidateToken(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if token.Method != jm.signingMethod {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(jm.secretKey), nil
	})
	
	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}
	
	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}
	
	claims, ok := token.Claims.(*JWTClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims")
	}
	
	// Additional validation
	if err := jm.validateClaims(claims); err != nil {
		return nil, err
	}
	
	return claims, nil
}

// validateClaims performs additional validation on JWT claims
func (jm *JWTManager) validateClaims(claims *JWTClaims) error {
	now := time.Now()
	
	// Check expiration
	if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(now) {
		return fmt.Errorf("token has expired")
	}
	
	// Check not before
	if claims.NotBefore != nil && claims.NotBefore.Time.After(now) {
		return fmt.Errorf("token not valid yet")
	}
	
	// Check issuer
	if claims.Issuer != jm.issuer {
		return fmt.Errorf("invalid token issuer")
	}
	
	// Check required fields
	if claims.UserID == "" {
		return fmt.Errorf("missing user ID in token")
	}
	
	if claims.SessionID == "" {
		return fmt.Errorf("missing session ID in token")
	}
	
	return nil
}

// RefreshToken generates a new access token using a refresh token
func (jm *JWTManager) RefreshToken(refreshTokenString string, userInfo UserInfo) (*TokenPair, error) {
	// Validate refresh token
	token, err := jwt.ParseWithClaims(refreshTokenString, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) {
		if token.Method != jm.signingMethod {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(jm.secretKey), nil
	})
	
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}
	
	if !token.Valid {
		return nil, fmt.Errorf("invalid refresh token")
	}
	
	claims, ok := token.Claims.(*jwt.RegisteredClaims)
	if !ok {
		return nil, fmt.Errorf("invalid refresh token claims")
	}
	
	// Validate refresh token claims
	if claims.Subject != userInfo.UserID {
		return nil, fmt.Errorf("refresh token user mismatch")
	}
	
	// Generate new token pair
	return jm.GenerateTokenPair(userInfo)
}

// ExtractTokenFromHeader extracts JWT token from Authorization header
func ExtractTokenFromHeader(authHeader string) (string, error) {
	if authHeader == "" {
		return "", fmt.Errorf("authorization header is required")
	}
	
	const bearerPrefix = "Bearer "
	if len(authHeader) < len(bearerPrefix) || authHeader[:len(bearerPrefix)] != bearerPrefix {
		return "", fmt.Errorf("authorization header must start with 'Bearer '")
	}
	
	token := authHeader[len(bearerPrefix):]
	if token == "" {
		return "", fmt.Errorf("token is required")
	}
	
	return token, nil
}

// TokenBlacklist interface for managing blacklisted tokens
type TokenBlacklist interface {
	Add(tokenID string, expiresAt time.Time) error
	IsBlacklisted(tokenID string) (bool, error)
	Cleanup() error
}

// InMemoryBlacklist is an in-memory implementation of TokenBlacklist
type InMemoryBlacklist struct {
	tokens map[string]time.Time
}

// NewInMemoryBlacklist creates a new in-memory blacklist
func NewInMemoryBlacklist() *InMemoryBlacklist {
	return &InMemoryBlacklist{
		tokens: make(map[string]time.Time),
	}
}

// Add adds a token to the blacklist
func (bl *InMemoryBlacklist) Add(tokenID string, expiresAt time.Time) error {
	bl.tokens[tokenID] = expiresAt
	return nil
}

// IsBlacklisted checks if a token is blacklisted
func (bl *InMemoryBlacklist) IsBlacklisted(tokenID string) (bool, error) {
	expiresAt, exists := bl.tokens[tokenID]
	if !exists {
		return false, nil
	}
	
	// If token has expired, remove it from blacklist
	if time.Now().After(expiresAt) {
		delete(bl.tokens, tokenID)
		return false, nil
	}
	
	return true, nil
}

// Cleanup removes expired tokens from the blacklist
func (bl *InMemoryBlacklist) Cleanup() error {
	now := time.Now()
	for tokenID, expiresAt := range bl.tokens {
		if now.After(expiresAt) {
			delete(bl.tokens, tokenID)
		}
	}
	return nil
}

// JWTManagerWithBlacklist extends JWTManager with blacklist support
type JWTManagerWithBlacklist struct {
	*JWTManager
	blacklist TokenBlacklist
}

// NewJWTManagerWithBlacklist creates a JWT manager with blacklist support
func NewJWTManagerWithBlacklist(secretKey, issuer string, accessTTL, refreshTTL time.Duration, blacklist TokenBlacklist) *JWTManagerWithBlacklist {
	return &JWTManagerWithBlacklist{
		JWTManager: NewJWTManager(secretKey, issuer, accessTTL, refreshTTL),
		blacklist:  blacklist,
	}
}

// ValidateTokenWithBlacklist validates a token and checks blacklist
func (jm *JWTManagerWithBlacklist) ValidateTokenWithBlacklist(tokenString string) (*JWTClaims, error) {
	claims, err := jm.ValidateToken(tokenString)
	if err != nil {
		return nil, err
	}
	
	// Check if token is blacklisted
	isBlacklisted, err := jm.blacklist.IsBlacklisted(claims.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to check token blacklist: %w", err)
	}
	
	if isBlacklisted {
		return nil, fmt.Errorf("token has been revoked")
	}
	
	return claims, nil
}

// RevokeToken adds a token to the blacklist
func (jm *JWTManagerWithBlacklist) RevokeToken(tokenString string) error {
	claims, err := jm.ValidateToken(tokenString)
	if err != nil {
		return fmt.Errorf("cannot revoke invalid token: %w", err)
	}
	
	return jm.blacklist.Add(claims.ID, claims.ExpiresAt.Time)
}
