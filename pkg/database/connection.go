package database

import (
	"context"
	"fmt"
	"log"
	"time"

	"web-api/internal/infrastructure/config"

	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// ConnectionConfig represents database connection configuration
type ConnectionConfig struct {
	Driver          string
	Host            string
	Port            string
	Username        string
	Password        string
	Database        string
	SSLMode         string
	Timezone        string
	
	// Connection Pool Settings
	MaxOpenConns    int
	MaxIdleConns    int
	ConnMaxLifetime time.Duration
	ConnMaxIdleTime time.Duration
	
	// Performance Settings
	LogLevel        logger.LogLevel
	SlowThreshold   time.Duration
	
	// Retry Settings
	MaxRetries      int
	RetryInterval   time.Duration
}

// DatabaseManager manages database connections and operations
type DatabaseManager struct {
	db     *gorm.DB
	config *ConnectionConfig
}

// NewConnection creates a new database connection with optimized settings
func NewConnection(cfg config.DatabaseConfiguration) (*gorm.DB, error) {
	connConfig := &ConnectionConfig{
		Driver:          cfg.Driver,
		Host:            cfg.Host,
		Port:            cfg.Port,
		Username:        cfg.Username,
		Password:        cfg.Password,
		Database:        cfg.Dbname,
		SSLMode:         getSSLMode(cfg.Sslmode),
		Timezone:        "UTC",
		
		// Optimized connection pool settings
		MaxOpenConns:    25,  // Maximum number of open connections
		MaxIdleConns:    10,  // Maximum number of idle connections
		ConnMaxLifetime: time.Hour,     // Maximum connection lifetime
		ConnMaxIdleTime: time.Minute * 30, // Maximum idle time
		
		// Performance settings
		LogLevel:      getLogLevel(cfg.Logmode),
		SlowThreshold: time.Second,
		
		// Retry settings
		MaxRetries:    3,
		RetryInterval: time.Second * 2,
	}
	
	manager := &DatabaseManager{config: connConfig}
	return manager.connect()
}

// connect establishes database connection with retry logic
func (dm *DatabaseManager) connect() (*gorm.DB, error) {
	var db *gorm.DB
	var err error
	
	for attempt := 1; attempt <= dm.config.MaxRetries; attempt++ {
		db, err = dm.attemptConnection()
		if err == nil {
			dm.db = db
			return db, nil
		}
		
		log.Printf("Database connection attempt %d failed: %v", attempt, err)
		
		if attempt < dm.config.MaxRetries {
			time.Sleep(dm.config.RetryInterval)
		}
	}
	
	return nil, fmt.Errorf("failed to connect to database after %d attempts: %w", dm.config.MaxRetries, err)
}

// attemptConnection attempts to establish a database connection
func (dm *DatabaseManager) attemptConnection() (*gorm.DB, error) {
	dsn, err := dm.buildDSN()
	if err != nil {
		return nil, err
	}
	
	// Configure GORM logger
	gormLogger := logger.New(
		log.New(log.Writer(), "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             dm.config.SlowThreshold,
			LogLevel:                  dm.config.LogLevel,
			IgnoreRecordNotFoundError: true,
			Colorful:                  false,
		},
	)
	
	// GORM configuration
	gormConfig := &gorm.Config{
		Logger:                 gormLogger,
		SkipDefaultTransaction: true, // Improve performance by skipping default transactions
		PrepareStmt:           true,  // Cache prepared statements
	}
	
	// Open database connection based on driver
	var db *gorm.DB
	switch dm.config.Driver {
	case "mysql":
		db, err = gorm.Open(mysql.Open(dsn), gormConfig)
	case "postgres":
		db, err = gorm.Open(postgres.Open(dsn), gormConfig)
	case "sqlserver":
		db, err = gorm.Open(sqlserver.Open(dsn), gormConfig)
	default:
		return nil, fmt.Errorf("unsupported database driver: %s", dm.config.Driver)
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}
	
	// Configure connection pool
	if err := dm.configureConnectionPool(db); err != nil {
		return nil, fmt.Errorf("failed to configure connection pool: %w", err)
	}
	
	// Test connection
	if err := dm.testConnection(db); err != nil {
		return nil, fmt.Errorf("database connection test failed: %w", err)
	}
	
	return db, nil
}

// buildDSN builds the database connection string
func (dm *DatabaseManager) buildDSN() (string, error) {
	switch dm.config.Driver {
	case "mysql":
		return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=%s",
			dm.config.Username,
			dm.config.Password,
			dm.config.Host,
			dm.config.Port,
			dm.config.Database,
			dm.config.Timezone,
		), nil
		
	case "postgres":
		return fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s TimeZone=%s",
			dm.config.Host,
			dm.config.Username,
			dm.config.Password,
			dm.config.Database,
			dm.config.Port,
			dm.config.SSLMode,
			dm.config.Timezone,
		), nil
		
	case "sqlserver":
		return fmt.Sprintf("sqlserver://%s:%s@%s:%s?database=%s&encrypt=%s",
			dm.config.Username,
			dm.config.Password,
			dm.config.Host,
			dm.config.Port,
			dm.config.Database,
			dm.config.SSLMode,
		), nil
		
	default:
		return "", fmt.Errorf("unsupported database driver: %s", dm.config.Driver)
	}
}

// configureConnectionPool configures the database connection pool
func (dm *DatabaseManager) configureConnectionPool(db *gorm.DB) error {
	sqlDB, err := db.DB()
	if err != nil {
		return err
	}
	
	// Set connection pool parameters
	sqlDB.SetMaxOpenConns(dm.config.MaxOpenConns)
	sqlDB.SetMaxIdleConns(dm.config.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(dm.config.ConnMaxLifetime)
	sqlDB.SetConnMaxIdleTime(dm.config.ConnMaxIdleTime)
	
	return nil
}

// testConnection tests the database connection
func (dm *DatabaseManager) testConnection(db *gorm.DB) error {
	sqlDB, err := db.DB()
	if err != nil {
		return err
	}
	
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	return sqlDB.PingContext(ctx)
}

// getSSLMode converts boolean SSL mode to string
func getSSLMode(sslMode bool) string {
	if sslMode {
		return "require"
	}
	return "disable"
}

// getLogLevel converts boolean log mode to GORM log level
func getLogLevel(logMode bool) logger.LogLevel {
	if logMode {
		return logger.Info
	}
	return logger.Silent
}

// HealthCheck performs a database health check
func (dm *DatabaseManager) HealthCheck(ctx context.Context) error {
	if dm.db == nil {
		return fmt.Errorf("database connection is nil")
	}
	
	sqlDB, err := dm.db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}
	
	return sqlDB.PingContext(ctx)
}

// GetStats returns database connection statistics
func (dm *DatabaseManager) GetStats() map[string]interface{} {
	if dm.db == nil {
		return map[string]interface{}{"error": "database connection is nil"}
	}
	
	sqlDB, err := dm.db.DB()
	if err != nil {
		return map[string]interface{}{"error": err.Error()}
	}
	
	stats := sqlDB.Stats()
	return map[string]interface{}{
		"max_open_connections":     stats.MaxOpenConnections,
		"open_connections":         stats.OpenConnections,
		"in_use":                  stats.InUse,
		"idle":                    stats.Idle,
		"wait_count":              stats.WaitCount,
		"wait_duration":           stats.WaitDuration.String(),
		"max_idle_closed":         stats.MaxIdleClosed,
		"max_idle_time_closed":    stats.MaxIdleTimeClosed,
		"max_lifetime_closed":     stats.MaxLifetimeClosed,
	}
}

// Close closes the database connection
func (dm *DatabaseManager) Close() error {
	if dm.db == nil {
		return nil
	}
	
	sqlDB, err := dm.db.DB()
	if err != nil {
		return err
	}
	
	return sqlDB.Close()
}

// Transaction executes a function within a database transaction
func (dm *DatabaseManager) Transaction(ctx context.Context, fn func(*gorm.DB) error) error {
	return dm.db.WithContext(ctx).Transaction(fn)
}

// ReadOnlyTransaction executes a read-only transaction
func (dm *DatabaseManager) ReadOnlyTransaction(ctx context.Context, fn func(*gorm.DB) error) error {
	return dm.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Set transaction to read-only
		if err := tx.Exec("SET TRANSACTION READ ONLY").Error; err != nil {
			return err
		}
		return fn(tx)
	})
}

// BulkInsert performs optimized bulk insert
func (dm *DatabaseManager) BulkInsert(ctx context.Context, records interface{}, batchSize int) error {
	return dm.db.WithContext(ctx).CreateInBatches(records, batchSize).Error
}

// OptimizeQueries provides query optimization hints
type QueryOptimizer struct {
	db *gorm.DB
}

// NewQueryOptimizer creates a new query optimizer
func NewQueryOptimizer(db *gorm.DB) *QueryOptimizer {
	return &QueryOptimizer{db: db}
}

// WithIndex adds index hint to query
func (qo *QueryOptimizer) WithIndex(indexName string) *gorm.DB {
	return qo.db.Set("gorm:query_hint", fmt.Sprintf("USE INDEX (%s)", indexName))
}

// WithReadReplica routes query to read replica
func (qo *QueryOptimizer) WithReadReplica() *gorm.DB {
	return qo.db.Set("gorm:query_hint", "/* read_replica */")
}

// WithTimeout adds query timeout
func (qo *QueryOptimizer) WithTimeout(timeout time.Duration) *gorm.DB {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	// Note: In production, you should manage the cancel function properly
	_ = cancel
	return qo.db.WithContext(ctx)
}

// DatabaseMetrics collects database performance metrics
type DatabaseMetrics struct {
	QueryCount    int64
	SlowQueries   int64
	ErrorCount    int64
	AvgQueryTime  time.Duration
	LastQueryTime time.Time
}

// MetricsCollector collects database metrics
type MetricsCollector struct {
	metrics *DatabaseMetrics
}

// NewMetricsCollector creates a new metrics collector
func NewMetricsCollector() *MetricsCollector {
	return &MetricsCollector{
		metrics: &DatabaseMetrics{},
	}
}

// GetMetrics returns current database metrics
func (mc *MetricsCollector) GetMetrics() *DatabaseMetrics {
	return mc.metrics
}

// RecordQuery records a database query execution
func (mc *MetricsCollector) RecordQuery(duration time.Duration, err error) {
	mc.metrics.QueryCount++
	mc.metrics.LastQueryTime = time.Now()
	
	if err != nil {
		mc.metrics.ErrorCount++
	}
	
	if duration > time.Second {
		mc.metrics.SlowQueries++
	}
	
	// Update average query time (simplified calculation)
	mc.metrics.AvgQueryTime = (mc.metrics.AvgQueryTime + duration) / 2
}
