package utils

import (
	"net/http"

	"web-api/internal/shared/errors"

	"github.com/gin-gonic/gin"
)

// StandardResponse represents the standard API response format
type StandardResponse struct {
	Code    int         `json:"code"`
	Data    interface{} `json:"data"`
	Message string      `json:"message"`
	Meta    *Meta       `json:"meta,omitempty"`
}

// Meta represents metadata for responses (pagination, etc.)
type Meta struct {
	Page       int   `json:"page,omitempty"`
	PageSize   int   `json:"pageSize,omitempty"`
	Total      int64 `json:"total,omitempty"`
	TotalPages int   `json:"totalPages,omitempty"`
}

// SuccessResponse creates a success response
func SuccessResponse(data interface{}, message string) StandardResponse {
	return StandardResponse{
		Code:    http.StatusOK,
		Data:    data,
		Message: message,
	}
}

// SuccessResponseWithMeta creates a success response with metadata
func SuccessResponseWithMeta(data interface{}, message string, meta *Meta) StandardResponse {
	return StandardResponse{
		Code:    http.StatusOK,
		Data:    data,
		Message: message,
		Meta:    meta,
	}
}

// ErrorResponse creates an error response
func ErrorResponse(code int, message string, details interface{}) StandardResponse {
	return StandardResponse{
		Code:    code,
		Data:    details,
		Message: message,
	}
}

// RespondWithSuccess sends a success response
func RespondWithSuccess(c *gin.Context, data interface{}, message string) {
	c.JSON(http.StatusOK, SuccessResponse(data, message))
}

// RespondWithSuccessAndMeta sends a success response with metadata
func RespondWithSuccessAndMeta(c *gin.Context, data interface{}, message string, meta *Meta) {
	c.JSON(http.StatusOK, SuccessResponseWithMeta(data, message, meta))
}

// RespondWithCreated sends a created response
func RespondWithCreated(c *gin.Context, data interface{}, message string) {
	c.JSON(http.StatusCreated, StandardResponse{
		Code:    http.StatusCreated,
		Data:    data,
		Message: message,
	})
}

// RespondWithNoContent sends a no content response
func RespondWithNoContent(c *gin.Context, message string) {
	c.JSON(http.StatusNoContent, StandardResponse{
		Code:    http.StatusNoContent,
		Data:    nil,
		Message: message,
	})
}

// RespondWithError sends an error response based on AppError
func RespondWithError(c *gin.Context, err *errors.AppError) {
	statusCode := err.GetStatusCode()
	
	response := StandardResponse{
		Code:    statusCode,
		Data:    err.Details,
		Message: err.Message,
	}
	
	c.JSON(statusCode, response)
}

// RespondWithValidationError sends a validation error response
func RespondWithValidationError(c *gin.Context, fieldErrors []errors.FieldError) {
	appError := errors.NewValidationErrors(fieldErrors)
	RespondWithError(c, appError)
}

// RespondWithNotFound sends a not found response
func RespondWithNotFound(c *gin.Context, resource, identifier string) {
	appError := errors.NewNotFoundError(resource, identifier)
	RespondWithError(c, appError)
}

// RespondWithUnauthorized sends an unauthorized response
func RespondWithUnauthorized(c *gin.Context, message string) {
	appError := errors.NewUnauthorizedError(message)
	RespondWithError(c, appError)
}

// RespondWithForbidden sends a forbidden response
func RespondWithForbidden(c *gin.Context, message string) {
	appError := errors.NewForbiddenError(message)
	RespondWithError(c, appError)
}

// RespondWithConflict sends a conflict response
func RespondWithConflict(c *gin.Context, message string) {
	appError := errors.NewConflictError(message)
	RespondWithError(c, appError)
}

// RespondWithInternalError sends an internal server error response
func RespondWithInternalError(c *gin.Context, message string, cause error) {
	appError := errors.NewInternalError(message, cause)
	RespondWithError(c, appError)
}

// RespondWithBadRequest sends a bad request response
func RespondWithBadRequest(c *gin.Context, message string) {
	appError := errors.NewValidationError(message)
	RespondWithError(c, appError)
}

// CreatePaginationMeta creates pagination metadata
func CreatePaginationMeta(page, pageSize int, total int64) *Meta {
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	
	return &Meta{
		Page:       page,
		PageSize:   pageSize,
		Total:      total,
		TotalPages: totalPages,
	}
}

// PaginatedResponse represents a paginated response
type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Pagination *Meta       `json:"pagination"`
}

// RespondWithPagination sends a paginated response
func RespondWithPagination(c *gin.Context, data interface{}, page, pageSize int, total int64, message string) {
	meta := CreatePaginationMeta(page, pageSize, total)
	RespondWithSuccessAndMeta(c, data, message, meta)
}

// HealthCheckResponse represents a health check response
type HealthCheckResponse struct {
	Status    string            `json:"status"`
	Timestamp string            `json:"timestamp"`
	Services  map[string]string `json:"services"`
	Version   string            `json:"version,omitempty"`
	Uptime    string            `json:"uptime,omitempty"`
}

// RespondWithHealthCheck sends a health check response
func RespondWithHealthCheck(c *gin.Context, health HealthCheckResponse) {
	statusCode := http.StatusOK
	if health.Status != "healthy" {
		statusCode = http.StatusServiceUnavailable
	}
	
	c.JSON(statusCode, health)
}

// APIVersionResponse represents an API version response
type APIVersionResponse struct {
	Version     string `json:"version"`
	BuildDate   string `json:"buildDate,omitempty"`
	GitCommit   string `json:"gitCommit,omitempty"`
	Environment string `json:"environment,omitempty"`
}

// RespondWithVersion sends an API version response
func RespondWithVersion(c *gin.Context, version APIVersionResponse) {
	c.JSON(http.StatusOK, version)
}

// FileResponse represents a file download response
type FileResponse struct {
	Filename    string `json:"filename"`
	ContentType string `json:"contentType"`
	Size        int64  `json:"size"`
}

// RespondWithFile sends a file download response
func RespondWithFile(c *gin.Context, filepath, filename, contentType string) {
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Type", contentType)
	c.File(filepath)
}

// RespondWithFileData sends file data as response
func RespondWithFileData(c *gin.Context, data []byte, filename, contentType string) {
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Type", contentType)
	c.Data(http.StatusOK, contentType, data)
}

// CacheResponse represents a cached response
type CacheResponse struct {
	Data      interface{} `json:"data"`
	CachedAt  string      `json:"cachedAt"`
	ExpiresAt string      `json:"expiresAt"`
	FromCache bool        `json:"fromCache"`
}

// RespondWithCache sends a cached response
func RespondWithCache(c *gin.Context, response CacheResponse, message string) {
	c.Header("X-Cache", "HIT")
	c.JSON(http.StatusOK, StandardResponse{
		Code:    http.StatusOK,
		Data:    response,
		Message: message,
	})
}

// AsyncResponse represents an asynchronous operation response
type AsyncResponse struct {
	JobID     string `json:"jobId"`
	Status    string `json:"status"`
	Message   string `json:"message"`
	StatusURL string `json:"statusUrl,omitempty"`
}

// RespondWithAsync sends an asynchronous operation response
func RespondWithAsync(c *gin.Context, response AsyncResponse) {
	c.JSON(http.StatusAccepted, StandardResponse{
		Code:    http.StatusAccepted,
		Data:    response,
		Message: "Request accepted for processing",
	})
}

// BulkResponse represents a bulk operation response
type BulkResponse struct {
	TotalRequested int                    `json:"totalRequested"`
	Successful     int                    `json:"successful"`
	Failed         int                    `json:"failed"`
	Errors         []BulkOperationError   `json:"errors,omitempty"`
	Results        []interface{}          `json:"results,omitempty"`
}

// BulkOperationError represents an error in bulk operation
type BulkOperationError struct {
	Index   int    `json:"index"`
	ID      string `json:"id,omitempty"`
	Message string `json:"message"`
}

// RespondWithBulkResult sends a bulk operation response
func RespondWithBulkResult(c *gin.Context, response BulkResponse, message string) {
	statusCode := http.StatusOK
	if response.Failed > 0 && response.Successful == 0 {
		statusCode = http.StatusBadRequest
	} else if response.Failed > 0 {
		statusCode = http.StatusMultiStatus
	}
	
	c.JSON(statusCode, StandardResponse{
		Code:    statusCode,
		Data:    response,
		Message: message,
	})
}
