package errors

import (
	"fmt"
	"net/http"
)

// ErrorType represents the type of error
type ErrorType string

const (
	ErrorTypeValidation   ErrorType = "VALIDATION_ERROR"
	ErrorTypeDomain       ErrorType = "DOMAIN_ERROR"
	ErrorTypeNotFound     ErrorType = "NOT_FOUND_ERROR"
	ErrorTypeConflict     ErrorType = "CONFLICT_ERROR"
	ErrorTypeUnauthorized ErrorType = "UNAUTHORIZED_ERROR"
	ErrorTypeForbidden    ErrorType = "FORBIDDEN_ERROR"
	ErrorTypeInternal     ErrorType = "INTERNAL_ERROR"
	ErrorTypeExternal     ErrorType = "EXTERNAL_ERROR"
)

// AppError represents a structured application error
type AppError struct {
	Type       ErrorType              `json:"type"`
	Message    string                 `json:"message"`
	Details    map[string]interface{} `json:"details,omitempty"`
	Cause      error                  `json:"-"`
	StatusCode int                    `json:"-"`
}

// Error implements the error interface
func (e *AppError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Type, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

// Unwrap returns the underlying cause
func (e *AppError) Unwrap() error {
	return e.Cause
}

// GetStatusCode returns the HTTP status code
func (e *AppError) GetStatusCode() int {
	if e.StatusCode != 0 {
		return e.StatusCode
	}
	
	switch e.Type {
	case ErrorTypeValidation:
		return http.StatusBadRequest
	case ErrorTypeDomain:
		return http.StatusBadRequest
	case ErrorTypeNotFound:
		return http.StatusNotFound
	case ErrorTypeConflict:
		return http.StatusConflict
	case ErrorTypeUnauthorized:
		return http.StatusUnauthorized
	case ErrorTypeForbidden:
		return http.StatusForbidden
	case ErrorTypeExternal:
		return http.StatusBadGateway
	default:
		return http.StatusInternalServerError
	}
}

// WithDetails adds details to the error
func (e *AppError) WithDetails(details map[string]interface{}) *AppError {
	e.Details = details
	return e
}

// WithCause adds a cause to the error
func (e *AppError) WithCause(cause error) *AppError {
	e.Cause = cause
	return e
}

// WithStatusCode sets a custom status code
func (e *AppError) WithStatusCode(code int) *AppError {
	e.StatusCode = code
	return e
}

// NewValidationError creates a new validation error
func NewValidationError(message string) *AppError {
	return &AppError{
		Type:    ErrorTypeValidation,
		Message: message,
	}
}

// NewValidationErrorWithDetails creates a validation error with details
func NewValidationErrorWithDetails(message string, details map[string]interface{}) *AppError {
	return &AppError{
		Type:    ErrorTypeValidation,
		Message: message,
		Details: details,
	}
}

// NewDomainError creates a new domain error
func NewDomainError(message string) *AppError {
	return &AppError{
		Type:    ErrorTypeDomain,
		Message: message,
	}
}

// NewNotFoundError creates a new not found error
func NewNotFoundError(resource string, identifier string) *AppError {
	return &AppError{
		Type:    ErrorTypeNotFound,
		Message: fmt.Sprintf("%s with identifier '%s' not found", resource, identifier),
		Details: map[string]interface{}{
			"resource":   resource,
			"identifier": identifier,
		},
	}
}

// NewConflictError creates a new conflict error
func NewConflictError(message string) *AppError {
	return &AppError{
		Type:    ErrorTypeConflict,
		Message: message,
	}
}

// NewUnauthorizedError creates a new unauthorized error
func NewUnauthorizedError(message string) *AppError {
	return &AppError{
		Type:    ErrorTypeUnauthorized,
		Message: message,
	}
}

// NewForbiddenError creates a new forbidden error
func NewForbiddenError(message string) *AppError {
	return &AppError{
		Type:    ErrorTypeForbidden,
		Message: message,
	}
}

// NewInternalError creates a new internal error
func NewInternalError(message string, cause error) *AppError {
	return &AppError{
		Type:    ErrorTypeInternal,
		Message: message,
		Cause:   cause,
	}
}

// NewExternalError creates a new external service error
func NewExternalError(service string, message string, cause error) *AppError {
	return &AppError{
		Type:    ErrorTypeExternal,
		Message: fmt.Sprintf("external service '%s' error: %s", service, message),
		Cause:   cause,
		Details: map[string]interface{}{
			"service": service,
		},
	}
}

// IsAppError checks if an error is an AppError
func IsAppError(err error) bool {
	_, ok := err.(*AppError)
	return ok
}

// AsAppError converts an error to AppError if possible
func AsAppError(err error) (*AppError, bool) {
	appErr, ok := err.(*AppError)
	return appErr, ok
}

// WrapError wraps a generic error into an AppError
func WrapError(err error, errorType ErrorType, message string) *AppError {
	if err == nil {
		return nil
	}
	
	if appErr, ok := AsAppError(err); ok {
		return appErr
	}
	
	return &AppError{
		Type:    errorType,
		Message: message,
		Cause:   err,
	}
}

// ErrorResponse represents the standard error response format
type ErrorResponse struct {
	Error ErrorDetail `json:"error"`
}

// ErrorDetail represents error details in the response
type ErrorDetail struct {
	Type    ErrorType              `json:"type"`
	Message string                 `json:"message"`
	Details map[string]interface{} `json:"details,omitempty"`
	Code    string                 `json:"code,omitempty"`
}

// ToErrorResponse converts an AppError to ErrorResponse
func (e *AppError) ToErrorResponse() ErrorResponse {
	return ErrorResponse{
		Error: ErrorDetail{
			Type:    e.Type,
			Message: e.Message,
			Details: e.Details,
		},
	}
}

// ValidationErrors represents multiple validation errors
type ValidationErrors struct {
	Errors []FieldError `json:"errors"`
}

// FieldError represents a field-specific validation error
type FieldError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Value   string `json:"value,omitempty"`
}

// Error implements the error interface for ValidationErrors
func (v ValidationErrors) Error() string {
	if len(v.Errors) == 0 {
		return "validation failed"
	}
	return fmt.Sprintf("validation failed: %s", v.Errors[0].Message)
}

// NewValidationErrors creates a new ValidationErrors
func NewValidationErrors(errors []FieldError) *AppError {
	return &AppError{
		Type:    ErrorTypeValidation,
		Message: "validation failed",
		Details: map[string]interface{}{
			"field_errors": errors,
		},
	}
}

// AddFieldError adds a field error to ValidationErrors
func (v *ValidationErrors) AddFieldError(field, message, value string) {
	v.Errors = append(v.Errors, FieldError{
		Field:   field,
		Message: message,
		Value:   value,
	})
}

// HasErrors returns true if there are validation errors
func (v *ValidationErrors) HasErrors() bool {
	return len(v.Errors) > 0
}

// ToAppError converts ValidationErrors to AppError
func (v *ValidationErrors) ToAppError() *AppError {
	if !v.HasErrors() {
		return nil
	}
	return NewValidationErrors(v.Errors)
}
