package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// CacheInterface defines the cache operations
type CacheInterface interface {
	Get(ctx context.Context, key string, dest interface{}) error
	Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error
	Delete(ctx context.Context, key string) error
	DeletePattern(ctx context.Context, pattern string) error
	Exists(ctx context.Context, key string) (bool, error)
	TTL(ctx context.Context, key string) (time.Duration, error)
	Increment(ctx context.Context, key string) (int64, error)
	Decrement(ctx context.Context, key string) (int64, error)
	SetNX(ctx context.Context, key string, value interface{}, ttl time.Duration) (bool, error)
	GetSet(ctx context.Context, key string, value interface{}) (string, error)
	MGet(ctx context.Context, keys []string) ([]interface{}, error)
	MSet(ctx context.Context, pairs map[string]interface{}, ttl time.Duration) error
	FlushAll(ctx context.Context) error
	Close() error
}

// RedisCache implements CacheInterface using Redis
type RedisCache struct {
	client *redis.Client
	prefix string
}

// RedisConfig represents Redis configuration
type RedisConfig struct {
	Host         string
	Port         string
	Password     string
	Database     int
	PoolSize     int
	MinIdleConns int
	MaxRetries   int
	DialTimeout  time.Duration
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	IdleTimeout  time.Duration
	Prefix       string
}

// NewRedisCache creates a new Redis cache instance
func NewRedisCache(config RedisConfig) (*RedisCache, error) {
	rdb := redis.NewClient(&redis.Options{
		Addr:         fmt.Sprintf("%s:%s", config.Host, config.Port),
		Password:     config.Password,
		DB:           config.Database,
		PoolSize:     config.PoolSize,
		MinIdleConns: config.MinIdleConns,
		MaxRetries:   config.MaxRetries,
		DialTimeout:  config.DialTimeout,
		ReadTimeout:  config.ReadTimeout,
		WriteTimeout: config.WriteTimeout,
		IdleTimeout:  config.IdleTimeout,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := rdb.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return &RedisCache{
		client: rdb,
		prefix: config.Prefix,
	}, nil
}

// buildKey builds cache key with prefix
func (r *RedisCache) buildKey(key string) string {
	if r.prefix == "" {
		return key
	}
	return fmt.Sprintf("%s:%s", r.prefix, key)
}

// Get retrieves a value from cache
func (r *RedisCache) Get(ctx context.Context, key string, dest interface{}) error {
	val, err := r.client.Get(ctx, r.buildKey(key)).Result()
	if err != nil {
		if err == redis.Nil {
			return ErrCacheMiss
		}
		return fmt.Errorf("failed to get cache key %s: %w", key, err)
	}

	if err := json.Unmarshal([]byte(val), dest); err != nil {
		return fmt.Errorf("failed to unmarshal cache value: %w", err)
	}

	return nil
}

// Set stores a value in cache
func (r *RedisCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal cache value: %w", err)
	}

	if err := r.client.Set(ctx, r.buildKey(key), data, ttl).Err(); err != nil {
		return fmt.Errorf("failed to set cache key %s: %w", key, err)
	}

	return nil
}

// Delete removes a key from cache
func (r *RedisCache) Delete(ctx context.Context, key string) error {
	if err := r.client.Del(ctx, r.buildKey(key)).Err(); err != nil {
		return fmt.Errorf("failed to delete cache key %s: %w", key, err)
	}
	return nil
}

// DeletePattern removes keys matching a pattern
func (r *RedisCache) DeletePattern(ctx context.Context, pattern string) error {
	keys, err := r.client.Keys(ctx, r.buildKey(pattern)).Result()
	if err != nil {
		return fmt.Errorf("failed to find keys with pattern %s: %w", pattern, err)
	}

	if len(keys) > 0 {
		if err := r.client.Del(ctx, keys...).Err(); err != nil {
			return fmt.Errorf("failed to delete keys with pattern %s: %w", pattern, err)
		}
	}

	return nil
}

// Exists checks if a key exists in cache
func (r *RedisCache) Exists(ctx context.Context, key string) (bool, error) {
	count, err := r.client.Exists(ctx, r.buildKey(key)).Result()
	if err != nil {
		return false, fmt.Errorf("failed to check existence of key %s: %w", key, err)
	}
	return count > 0, nil
}

// TTL returns the time to live for a key
func (r *RedisCache) TTL(ctx context.Context, key string) (time.Duration, error) {
	ttl, err := r.client.TTL(ctx, r.buildKey(key)).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to get TTL for key %s: %w", key, err)
	}
	return ttl, nil
}

// Increment increments a numeric value
func (r *RedisCache) Increment(ctx context.Context, key string) (int64, error) {
	val, err := r.client.Incr(ctx, r.buildKey(key)).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to increment key %s: %w", key, err)
	}
	return val, nil
}

// Decrement decrements a numeric value
func (r *RedisCache) Decrement(ctx context.Context, key string) (int64, error) {
	val, err := r.client.Decr(ctx, r.buildKey(key)).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to decrement key %s: %w", key, err)
	}
	return val, nil
}

// SetNX sets a key only if it doesn't exist
func (r *RedisCache) SetNX(ctx context.Context, key string, value interface{}, ttl time.Duration) (bool, error) {
	data, err := json.Marshal(value)
	if err != nil {
		return false, fmt.Errorf("failed to marshal cache value: %w", err)
	}

	success, err := r.client.SetNX(ctx, r.buildKey(key), data, ttl).Result()
	if err != nil {
		return false, fmt.Errorf("failed to set NX cache key %s: %w", key, err)
	}

	return success, nil
}

// GetSet atomically sets a key and returns its old value
func (r *RedisCache) GetSet(ctx context.Context, key string, value interface{}) (string, error) {
	data, err := json.Marshal(value)
	if err != nil {
		return "", fmt.Errorf("failed to marshal cache value: %w", err)
	}

	oldVal, err := r.client.GetSet(ctx, r.buildKey(key), data).Result()
	if err != nil && err != redis.Nil {
		return "", fmt.Errorf("failed to get set cache key %s: %w", key, err)
	}

	return oldVal, nil
}

// MGet gets multiple keys at once
func (r *RedisCache) MGet(ctx context.Context, keys []string) ([]interface{}, error) {
	redisKeys := make([]string, len(keys))
	for i, key := range keys {
		redisKeys[i] = r.buildKey(key)
	}

	vals, err := r.client.MGet(ctx, redisKeys...).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get multiple cache keys: %w", err)
	}

	return vals, nil
}

// MSet sets multiple keys at once
func (r *RedisCache) MSet(ctx context.Context, pairs map[string]interface{}, ttl time.Duration) error {
	pipe := r.client.Pipeline()

	for key, value := range pairs {
		data, err := json.Marshal(value)
		if err != nil {
			return fmt.Errorf("failed to marshal cache value for key %s: %w", key, err)
		}
		pipe.Set(ctx, r.buildKey(key), data, ttl)
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to set multiple cache keys: %w", err)
	}

	return nil
}

// FlushAll removes all keys from cache
func (r *RedisCache) FlushAll(ctx context.Context) error {
	if err := r.client.FlushAll(ctx).Err(); err != nil {
		return fmt.Errorf("failed to flush all cache: %w", err)
	}
	return nil
}

// Close closes the Redis connection
func (r *RedisCache) Close() error {
	return r.client.Close()
}

// GetStats returns Redis statistics
func (r *RedisCache) GetStats(ctx context.Context) (map[string]string, error) {
	info, err := r.client.Info(ctx).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get Redis info: %w", err)
	}

	stats := make(map[string]string)
	// Parse Redis info string and extract relevant statistics
	// This is a simplified version - you might want to parse more fields
	stats["redis_info"] = info

	return stats, nil
}

// MemoryCache implements CacheInterface using in-memory storage
type MemoryCache struct {
	data map[string]cacheItem
}

type cacheItem struct {
	value     interface{}
	expiresAt time.Time
}

// NewMemoryCache creates a new in-memory cache
func NewMemoryCache() *MemoryCache {
	cache := &MemoryCache{
		data: make(map[string]cacheItem),
	}

	// Start cleanup goroutine
	go cache.cleanup()

	return cache
}

// Get retrieves a value from memory cache
func (m *MemoryCache) Get(ctx context.Context, key string, dest interface{}) error {
	item, exists := m.data[key]
	if !exists {
		return ErrCacheMiss
	}

	if time.Now().After(item.expiresAt) {
		delete(m.data, key)
		return ErrCacheMiss
	}

	// Simple type assertion - in production, you might want more sophisticated handling
	data, err := json.Marshal(item.value)
	if err != nil {
		return fmt.Errorf("failed to marshal cached value: %w", err)
	}

	if err := json.Unmarshal(data, dest); err != nil {
		return fmt.Errorf("failed to unmarshal cached value: %w", err)
	}

	return nil
}

// Set stores a value in memory cache
func (m *MemoryCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	expiresAt := time.Now().Add(ttl)
	if ttl == 0 {
		expiresAt = time.Now().Add(24 * time.Hour) // Default 24 hours
	}

	m.data[key] = cacheItem{
		value:     value,
		expiresAt: expiresAt,
	}

	return nil
}

// Delete removes a key from memory cache
func (m *MemoryCache) Delete(ctx context.Context, key string) error {
	delete(m.data, key)
	return nil
}

// DeletePattern removes keys matching a pattern (simplified implementation)
func (m *MemoryCache) DeletePattern(ctx context.Context, pattern string) error {
	// Simple pattern matching - in production, you might want regex support
	for key := range m.data {
		if key == pattern {
			delete(m.data, key)
		}
	}
	return nil
}

// Exists checks if a key exists in memory cache
func (m *MemoryCache) Exists(ctx context.Context, key string) (bool, error) {
	item, exists := m.data[key]
	if !exists {
		return false, nil
	}

	if time.Now().After(item.expiresAt) {
		delete(m.data, key)
		return false, nil
	}

	return true, nil
}

// TTL returns the time to live for a key
func (m *MemoryCache) TTL(ctx context.Context, key string) (time.Duration, error) {
	item, exists := m.data[key]
	if !exists {
		return 0, ErrCacheMiss
	}

	ttl := time.Until(item.expiresAt)
	if ttl < 0 {
		delete(m.data, key)
		return 0, ErrCacheMiss
	}

	return ttl, nil
}

// Increment increments a numeric value (simplified implementation)
func (m *MemoryCache) Increment(ctx context.Context, key string) (int64, error) {
	// Simplified implementation
	return 0, fmt.Errorf("increment not implemented for memory cache")
}

// Decrement decrements a numeric value (simplified implementation)
func (m *MemoryCache) Decrement(ctx context.Context, key string) (int64, error) {
	// Simplified implementation
	return 0, fmt.Errorf("decrement not implemented for memory cache")
}

// SetNX sets a key only if it doesn't exist
func (m *MemoryCache) SetNX(ctx context.Context, key string, value interface{}, ttl time.Duration) (bool, error) {
	if _, exists := m.data[key]; exists {
		return false, nil
	}

	return true, m.Set(ctx, key, value, ttl)
}

// GetSet atomically sets a key and returns its old value
func (m *MemoryCache) GetSet(ctx context.Context, key string, value interface{}) (string, error) {
	var oldValue string
	if item, exists := m.data[key]; exists {
		data, _ := json.Marshal(item.value)
		oldValue = string(data)
	}

	m.Set(ctx, key, value, 0)
	return oldValue, nil
}

// MGet gets multiple keys at once
func (m *MemoryCache) MGet(ctx context.Context, keys []string) ([]interface{}, error) {
	results := make([]interface{}, len(keys))
	for i, key := range keys {
		if item, exists := m.data[key]; exists && time.Now().Before(item.expiresAt) {
			results[i] = item.value
		}
	}
	return results, nil
}

// MSet sets multiple keys at once
func (m *MemoryCache) MSet(ctx context.Context, pairs map[string]interface{}, ttl time.Duration) error {
	for key, value := range pairs {
		if err := m.Set(ctx, key, value, ttl); err != nil {
			return err
		}
	}
	return nil
}

// FlushAll removes all keys from memory cache
func (m *MemoryCache) FlushAll(ctx context.Context) error {
	m.data = make(map[string]cacheItem)
	return nil
}

// Close closes the memory cache (no-op)
func (m *MemoryCache) Close() error {
	return nil
}

// cleanup removes expired items from memory cache
func (m *MemoryCache) cleanup() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		now := time.Now()
		for key, item := range m.data {
			if now.After(item.expiresAt) {
				delete(m.data, key)
			}
		}
	}
}

// Common cache errors
var (
	ErrCacheMiss = fmt.Errorf("cache miss")
)

// CacheManager manages multiple cache instances
type CacheManager struct {
	primary   CacheInterface
	secondary CacheInterface
}

// NewCacheManager creates a new cache manager with primary and secondary caches
func NewCacheManager(primary, secondary CacheInterface) *CacheManager {
	return &CacheManager{
		primary:   primary,
		secondary: secondary,
	}
}

// Get tries primary cache first, then secondary
func (cm *CacheManager) Get(ctx context.Context, key string, dest interface{}) error {
	err := cm.primary.Get(ctx, key, dest)
	if err == nil {
		return nil
	}

	if cm.secondary != nil {
		return cm.secondary.Get(ctx, key, dest)
	}

	return err
}

// Set stores in both primary and secondary caches
func (cm *CacheManager) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	err := cm.primary.Set(ctx, key, value, ttl)
	if cm.secondary != nil {
		cm.secondary.Set(ctx, key, value, ttl) // Ignore secondary errors
	}
	return err
}
