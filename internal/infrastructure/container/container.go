package container

import (
	"context"
	"fmt"
	"log"

	"web-api/internal/application/usecases/keymodel"
	"web-api/internal/application/usecases/lookup"
	"web-api/internal/domain/repositories"
	"web-api/internal/infrastructure/config"
	"web-api/internal/infrastructure/logging"
	"web-api/internal/infrastructure/persistence/sqlserver"
	"web-api/internal/presentation/http/handlers"
	"web-api/pkg/database"
	pkglogger "web-api/pkg/logger"

	"gorm.io/gorm"
)

// Container holds all application dependencies
type Container struct {
	// Configuration
	Config *config.Config
	Logger pkglogger.Logger

	// Database
	DB *gorm.DB

	// Repositories
	KeyModelRepo repositories.KeyModelRepository
	LookupRepo   repositories.LookupRepository

	// Use Cases
	CreateKeyModelUC *keymodel.CreateKeyModelUseCase
	UpdateKeyModelUC *keymodel.UpdateKeyModelUseCase
	GetKeyModelUC    *keymodel.GetKeyModelUseCase
	DeleteKeyModelUC *keymodel.DeleteKeyModelUseCase
	ListKeyModelUC   *keymodel.ListKeyModelUseCase

	ManageSeasonsUC   *lookup.ManageSeasonsUseCase
	ManageFactoriesUC *lookup.ManageFactoriesUseCase
	ManageTypesUC     *lookup.ManageKeyModelTypesUseCase

	// Handlers
	KeyModelHandler *handlers.KeyModelHandler
	LookupHandler   *handlers.LookupHandler
	HealthHandler   *handlers.HealthHandler
}

// NewContainer creates and wires all dependencies
func NewContainer(ctx context.Context) (*Container, error) {
	container := &Container{}

	// Initialize configuration
	if err := container.initConfig(); err != nil {
		return nil, fmt.Errorf("failed to initialize config: %w", err)
	}

	// Initialize logger
	if err := container.initLogger(); err != nil {
		return nil, fmt.Errorf("failed to initialize logger: %w", err)
	}

	// Initialize database
	if err := container.initDatabase(); err != nil {
		return nil, fmt.Errorf("failed to initialize database: %w", err)
	}

	// Initialize repositories
	container.initRepositories()

	// Initialize use cases
	container.initUseCases()

	// Initialize handlers
	container.initHandlers()

	container.Logger.Info(ctx, "Container initialized successfully")
	return container, nil
}

// initConfig initializes configuration
func (c *Container) initConfig() error {
	cfg, err := config.Load()
	if err != nil {
		return err
	}
	c.Config = cfg
	return nil
}

// initLogger initializes structured logger
func (c *Container) initLogger() error {
	logger, err := logging.NewStructuredLogger(c.Config.Log)
	if err != nil {
		return err
	}
	c.Logger = logger
	return nil
}

// initDatabase initializes database connection
func (c *Container) initDatabase() error {
	db, err := database.NewConnection(c.Config.Database)
	if err != nil {
		return err
	}
	c.DB = db
	return nil
}

// initRepositories initializes all repositories
func (c *Container) initRepositories() {
	c.KeyModelRepo = sqlserver.NewKeyModelRepository(c.DB, c.Logger)
	c.LookupRepo = sqlserver.NewLookupRepository(c.DB, c.Logger)
}

// initUseCases initializes all use cases
func (c *Container) initUseCases() {
	// Key Model Use Cases
	c.CreateKeyModelUC = keymodel.NewCreateKeyModelUseCase(c.KeyModelRepo, c.Logger)
	c.UpdateKeyModelUC = keymodel.NewUpdateKeyModelUseCase(c.KeyModelRepo, c.Logger)
	c.GetKeyModelUC = keymodel.NewGetKeyModelUseCase(c.KeyModelRepo, c.Logger)
	c.DeleteKeyModelUC = keymodel.NewDeleteKeyModelUseCase(c.KeyModelRepo, c.Logger)
	c.ListKeyModelUC = keymodel.NewListKeyModelUseCase(c.KeyModelRepo, c.Logger)

	// Lookup Use Cases
	c.ManageSeasonsUC = lookup.NewManageSeasonsUseCase(c.LookupRepo, c.Logger)
	c.ManageFactoriesUC = lookup.NewManageFactoriesUseCase(c.LookupRepo, c.Logger)
	c.ManageTypesUC = lookup.NewManageKeyModelTypesUseCase(c.LookupRepo, c.Logger)
}

// initHandlers initializes all HTTP handlers
func (c *Container) initHandlers() {
	c.KeyModelHandler = handlers.NewKeyModelHandler(
		c.CreateKeyModelUC,
		c.UpdateKeyModelUC,
		c.GetKeyModelUC,
		c.DeleteKeyModelUC,
		c.ListKeyModelUC,
		c.Logger,
	)

	c.LookupHandler = handlers.NewLookupHandler(
		c.ManageSeasonsUC,
		c.ManageFactoriesUC,
		c.ManageTypesUC,
		c.Logger,
	)

	c.HealthHandler = handlers.NewHealthHandler(c.DB, c.Logger)
}

// Cleanup performs cleanup operations
func (c *Container) Cleanup() error {
	if c.DB != nil {
		sqlDB, err := c.DB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// GetDB returns database instance for health checks
func (c *Container) GetDB() *gorm.DB {
	return c.DB
}

// GetLogger returns logger instance
func (c *Container) GetLogger() pkglogger.Logger {
	return c.Logger
}

// GetConfig returns configuration
func (c *Container) GetConfig() *config.Config {
	return c.Config
}

// Validate ensures all dependencies are properly initialized
func (c *Container) Validate() error {
	if c.Config == nil {
		return fmt.Errorf("config is not initialized")
	}
	if c.Logger == nil {
		return fmt.Errorf("logger is not initialized")
	}
	if c.DB == nil {
		return fmt.Errorf("database is not initialized")
	}
	if c.KeyModelRepo == nil {
		return fmt.Errorf("key model repository is not initialized")
	}
	if c.LookupRepo == nil {
		return fmt.Errorf("lookup repository is not initialized")
	}
	return nil
}

// Health checks the health of all dependencies
func (c *Container) Health(ctx context.Context) map[string]string {
	health := make(map[string]string)

	// Check database
	if c.DB != nil {
		sqlDB, err := c.DB.DB()
		if err != nil {
			health["database"] = "error: " + err.Error()
		} else if err := sqlDB.PingContext(ctx); err != nil {
			health["database"] = "error: " + err.Error()
		} else {
			health["database"] = "healthy"
		}
	} else {
		health["database"] = "not initialized"
	}

	// Check logger
	if c.Logger != nil {
		health["logger"] = "healthy"
	} else {
		health["logger"] = "not initialized"
	}

	// Check config
	if c.Config != nil {
		health["config"] = "healthy"
	} else {
		health["config"] = "not initialized"
	}

	return health
}

// LogDependencies logs all initialized dependencies for debugging
func (c *Container) LogDependencies(ctx context.Context) {
	c.Logger.Info(ctx, "Container dependencies initialized",
		"config", c.Config != nil,
		"logger", c.Logger != nil,
		"database", c.DB != nil,
		"keyModelRepo", c.KeyModelRepo != nil,
		"lookupRepo", c.LookupRepo != nil,
		"createKeyModelUC", c.CreateKeyModelUC != nil,
		"updateKeyModelUC", c.UpdateKeyModelUC != nil,
		"getKeyModelUC", c.GetKeyModelUC != nil,
		"deleteKeyModelUC", c.DeleteKeyModelUC != nil,
		"listKeyModelUC", c.ListKeyModelUC != nil,
		"keyModelHandler", c.KeyModelHandler != nil,
		"lookupHandler", c.LookupHandler != nil,
		"healthHandler", c.HealthHandler != nil,
	)
}

// NewTestContainer creates a container for testing with mocked dependencies
func NewTestContainer() *Container {
	return &Container{
		// Initialize with test/mock implementations
	}
}

// SetLogger allows setting a custom logger (useful for testing)
func (c *Container) SetLogger(logger pkglogger.Logger) {
	c.Logger = logger
}

// SetDB allows setting a custom database (useful for testing)
func (c *Container) SetDB(db *gorm.DB) {
	c.DB = db
}
