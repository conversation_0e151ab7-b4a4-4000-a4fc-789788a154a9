package monitoring

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// MetricsCollector defines the interface for metrics collection
type MetricsCollector interface {
	// HTTP Metrics
	RecordHTTPRequest(method, path string, statusCode int, duration time.Duration)
	RecordHTTPRequestSize(method, path string, size int64)
	RecordHTTPResponseSize(method, path string, size int64)
	
	// Database Metrics
	RecordDatabaseQuery(operation string, duration time.Duration, success bool)
	RecordDatabaseConnection(pool string, active, idle int)
	
	// Business Metrics
	RecordKeyModelCreated(factory, season string)
	RecordKeyModelUpdated(factory, season string)
	RecordKeyModelDeleted(factory, season string)
	RecordStageAdvancement(fromStage, toStage string)
	
	// Cache Metrics
	RecordCacheHit(cacheType string)
	RecordCacheMiss(cacheType string)
	RecordCacheOperation(operation string, duration time.Duration)
	
	// Error Metrics
	RecordError(errorType, component string)
	RecordPanic(component string)
	
	// Custom Metrics
	IncrementCounter(name string, labels map[string]string)
	RecordHistogram(name string, value float64, labels map[string]string)
	SetGauge(name string, value float64, labels map[string]string)
}

// PrometheusMetrics implements MetricsCollector using Prometheus
type PrometheusMetrics struct {
	// HTTP Metrics
	httpRequestsTotal    *prometheus.CounterVec
	httpRequestDuration  *prometheus.HistogramVec
	httpRequestSize      *prometheus.HistogramVec
	httpResponseSize     *prometheus.HistogramVec
	
	// Database Metrics
	dbQueriesTotal       *prometheus.CounterVec
	dbQueryDuration      *prometheus.HistogramVec
	dbConnections        *prometheus.GaugeVec
	
	// Business Metrics
	keyModelOperations   *prometheus.CounterVec
	stageAdvancements    *prometheus.CounterVec
	
	// Cache Metrics
	cacheOperations      *prometheus.CounterVec
	cacheHitRatio        *prometheus.GaugeVec
	cacheDuration        *prometheus.HistogramVec
	
	// Error Metrics
	errorsTotal          *prometheus.CounterVec
	panicsTotal          *prometheus.CounterVec
	
	// System Metrics
	activeConnections    *prometheus.GaugeVec
	memoryUsage          *prometheus.GaugeVec
	cpuUsage             *prometheus.GaugeVec
	
	registry *prometheus.Registry
}

// NewPrometheusMetrics creates a new Prometheus metrics collector
func NewPrometheusMetrics() *PrometheusMetrics {
	registry := prometheus.NewRegistry()
	
	metrics := &PrometheusMetrics{
		// HTTP Metrics
		httpRequestsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "http_requests_total",
				Help: "Total number of HTTP requests",
			},
			[]string{"method", "path", "status_code"},
		),
		httpRequestDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_request_duration_seconds",
				Help:    "HTTP request duration in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"method", "path"},
		),
		httpRequestSize: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_request_size_bytes",
				Help:    "HTTP request size in bytes",
				Buckets: prometheus.ExponentialBuckets(100, 10, 8),
			},
			[]string{"method", "path"},
		),
		httpResponseSize: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_response_size_bytes",
				Help:    "HTTP response size in bytes",
				Buckets: prometheus.ExponentialBuckets(100, 10, 8),
			},
			[]string{"method", "path"},
		),
		
		// Database Metrics
		dbQueriesTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "database_queries_total",
				Help: "Total number of database queries",
			},
			[]string{"operation", "success"},
		),
		dbQueryDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "database_query_duration_seconds",
				Help:    "Database query duration in seconds",
				Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5},
			},
			[]string{"operation"},
		),
		dbConnections: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "database_connections",
				Help: "Number of database connections",
			},
			[]string{"pool", "state"},
		),
		
		// Business Metrics
		keyModelOperations: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "key_model_operations_total",
				Help: "Total number of key model operations",
			},
			[]string{"operation", "factory", "season"},
		),
		stageAdvancements: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "stage_advancements_total",
				Help: "Total number of stage advancements",
			},
			[]string{"from_stage", "to_stage"},
		),
		
		// Cache Metrics
		cacheOperations: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "cache_operations_total",
				Help: "Total number of cache operations",
			},
			[]string{"type", "operation", "result"},
		),
		cacheHitRatio: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "cache_hit_ratio",
				Help: "Cache hit ratio",
			},
			[]string{"type"},
		),
		cacheDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "cache_operation_duration_seconds",
				Help:    "Cache operation duration in seconds",
				Buckets: []float64{0.0001, 0.0005, 0.001, 0.005, 0.01, 0.05, 0.1},
			},
			[]string{"operation"},
		),
		
		// Error Metrics
		errorsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "errors_total",
				Help: "Total number of errors",
			},
			[]string{"type", "component"},
		),
		panicsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "panics_total",
				Help: "Total number of panics",
			},
			[]string{"component"},
		),
		
		// System Metrics
		activeConnections: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "active_connections",
				Help: "Number of active connections",
			},
			[]string{"type"},
		),
		memoryUsage: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "memory_usage_bytes",
				Help: "Memory usage in bytes",
			},
			[]string{"type"},
		),
		cpuUsage: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "cpu_usage_percent",
				Help: "CPU usage percentage",
			},
			[]string{"core"},
		),
		
		registry: registry,
	}
	
	// Register all metrics
	metrics.registerMetrics()
	
	return metrics
}

// registerMetrics registers all metrics with the registry
func (pm *PrometheusMetrics) registerMetrics() {
	pm.registry.MustRegister(
		pm.httpRequestsTotal,
		pm.httpRequestDuration,
		pm.httpRequestSize,
		pm.httpResponseSize,
		pm.dbQueriesTotal,
		pm.dbQueryDuration,
		pm.dbConnections,
		pm.keyModelOperations,
		pm.stageAdvancements,
		pm.cacheOperations,
		pm.cacheHitRatio,
		pm.cacheDuration,
		pm.errorsTotal,
		pm.panicsTotal,
		pm.activeConnections,
		pm.memoryUsage,
		pm.cpuUsage,
	)
}

// HTTP Metrics Implementation
func (pm *PrometheusMetrics) RecordHTTPRequest(method, path string, statusCode int, duration time.Duration) {
	pm.httpRequestsTotal.WithLabelValues(method, path, strconv.Itoa(statusCode)).Inc()
	pm.httpRequestDuration.WithLabelValues(method, path).Observe(duration.Seconds())
}

func (pm *PrometheusMetrics) RecordHTTPRequestSize(method, path string, size int64) {
	pm.httpRequestSize.WithLabelValues(method, path).Observe(float64(size))
}

func (pm *PrometheusMetrics) RecordHTTPResponseSize(method, path string, size int64) {
	pm.httpResponseSize.WithLabelValues(method, path).Observe(float64(size))
}

// Database Metrics Implementation
func (pm *PrometheusMetrics) RecordDatabaseQuery(operation string, duration time.Duration, success bool) {
	successStr := "false"
	if success {
		successStr = "true"
	}
	pm.dbQueriesTotal.WithLabelValues(operation, successStr).Inc()
	pm.dbQueryDuration.WithLabelValues(operation).Observe(duration.Seconds())
}

func (pm *PrometheusMetrics) RecordDatabaseConnection(pool string, active, idle int) {
	pm.dbConnections.WithLabelValues(pool, "active").Set(float64(active))
	pm.dbConnections.WithLabelValues(pool, "idle").Set(float64(idle))
}

// Business Metrics Implementation
func (pm *PrometheusMetrics) RecordKeyModelCreated(factory, season string) {
	pm.keyModelOperations.WithLabelValues("create", factory, season).Inc()
}

func (pm *PrometheusMetrics) RecordKeyModelUpdated(factory, season string) {
	pm.keyModelOperations.WithLabelValues("update", factory, season).Inc()
}

func (pm *PrometheusMetrics) RecordKeyModelDeleted(factory, season string) {
	pm.keyModelOperations.WithLabelValues("delete", factory, season).Inc()
}

func (pm *PrometheusMetrics) RecordStageAdvancement(fromStage, toStage string) {
	pm.stageAdvancements.WithLabelValues(fromStage, toStage).Inc()
}

// Cache Metrics Implementation
func (pm *PrometheusMetrics) RecordCacheHit(cacheType string) {
	pm.cacheOperations.WithLabelValues(cacheType, "get", "hit").Inc()
}

func (pm *PrometheusMetrics) RecordCacheMiss(cacheType string) {
	pm.cacheOperations.WithLabelValues(cacheType, "get", "miss").Inc()
}

func (pm *PrometheusMetrics) RecordCacheOperation(operation string, duration time.Duration) {
	pm.cacheDuration.WithLabelValues(operation).Observe(duration.Seconds())
}

// Error Metrics Implementation
func (pm *PrometheusMetrics) RecordError(errorType, component string) {
	pm.errorsTotal.WithLabelValues(errorType, component).Inc()
}

func (pm *PrometheusMetrics) RecordPanic(component string) {
	pm.panicsTotal.WithLabelValues(component).Inc()
}

// Custom Metrics Implementation
func (pm *PrometheusMetrics) IncrementCounter(name string, labels map[string]string) {
	// Implementation for dynamic counters
	// This would require a more complex setup with dynamic metric creation
}

func (pm *PrometheusMetrics) RecordHistogram(name string, value float64, labels map[string]string) {
	// Implementation for dynamic histograms
}

func (pm *PrometheusMetrics) SetGauge(name string, value float64, labels map[string]string) {
	// Implementation for dynamic gauges
}

// GetHandler returns the Prometheus metrics HTTP handler
func (pm *PrometheusMetrics) GetHandler() http.Handler {
	return promhttp.HandlerFor(pm.registry, promhttp.HandlerOpts{})
}

// MetricsMiddleware creates middleware for automatic HTTP metrics collection
func (pm *PrometheusMetrics) MetricsMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()
			
			// Create response writer wrapper to capture status code and size
			wrapped := &metricsResponseWriter{ResponseWriter: w, statusCode: 200}
			
			// Record request size
			if r.ContentLength > 0 {
				pm.RecordHTTPRequestSize(r.Method, r.URL.Path, r.ContentLength)
			}
			
			// Process request
			next.ServeHTTP(wrapped, r)
			
			// Record metrics
			duration := time.Since(start)
			pm.RecordHTTPRequest(r.Method, r.URL.Path, wrapped.statusCode, duration)
			pm.RecordHTTPResponseSize(r.Method, r.URL.Path, int64(wrapped.size))
		})
	}
}

// metricsResponseWriter wraps http.ResponseWriter to capture metrics
type metricsResponseWriter struct {
	http.ResponseWriter
	statusCode int
	size       int
}

func (mrw *metricsResponseWriter) WriteHeader(code int) {
	mrw.statusCode = code
	mrw.ResponseWriter.WriteHeader(code)
}

func (mrw *metricsResponseWriter) Write(b []byte) (int, error) {
	size, err := mrw.ResponseWriter.Write(b)
	mrw.size += size
	return size, err
}

// HealthChecker provides health check functionality
type HealthChecker struct {
	checks map[string]HealthCheck
}

// HealthCheck represents a health check function
type HealthCheck func(ctx context.Context) error

// HealthStatus represents the health status of a component
type HealthStatus struct {
	Status    string            `json:"status"`
	Timestamp time.Time         `json:"timestamp"`
	Checks    map[string]string `json:"checks"`
	Version   string            `json:"version,omitempty"`
	Uptime    string            `json:"uptime,omitempty"`
}

// NewHealthChecker creates a new health checker
func NewHealthChecker() *HealthChecker {
	return &HealthChecker{
		checks: make(map[string]HealthCheck),
	}
}

// AddCheck adds a health check
func (hc *HealthChecker) AddCheck(name string, check HealthCheck) {
	hc.checks[name] = check
}

// Check performs all health checks
func (hc *HealthChecker) Check(ctx context.Context) HealthStatus {
	status := HealthStatus{
		Status:    "healthy",
		Timestamp: time.Now(),
		Checks:    make(map[string]string),
	}
	
	for name, check := range hc.checks {
		if err := check(ctx); err != nil {
			status.Checks[name] = fmt.Sprintf("unhealthy: %v", err)
			status.Status = "unhealthy"
		} else {
			status.Checks[name] = "healthy"
		}
	}
	
	return status
}

// GetHealthHandler returns an HTTP handler for health checks
func (hc *HealthChecker) GetHealthHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()
		
		health := hc.Check(ctx)
		
		w.Header().Set("Content-Type", "application/json")
		if health.Status == "healthy" {
			w.WriteHeader(http.StatusOK)
		} else {
			w.WriteHeader(http.StatusServiceUnavailable)
		}
		
		json.NewEncoder(w).Encode(health)
	}
}

// SystemMetricsCollector collects system-level metrics
type SystemMetricsCollector struct {
	metrics *PrometheusMetrics
}

// NewSystemMetricsCollector creates a new system metrics collector
func NewSystemMetricsCollector(metrics *PrometheusMetrics) *SystemMetricsCollector {
	return &SystemMetricsCollector{
		metrics: metrics,
	}
}

// Start begins collecting system metrics
func (smc *SystemMetricsCollector) Start(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			smc.collectMetrics()
		}
	}
}

// collectMetrics collects various system metrics
func (smc *SystemMetricsCollector) collectMetrics() {
	// Collect memory metrics
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	smc.metrics.memoryUsage.WithLabelValues("heap_alloc").Set(float64(m.HeapAlloc))
	smc.metrics.memoryUsage.WithLabelValues("heap_sys").Set(float64(m.HeapSys))
	smc.metrics.memoryUsage.WithLabelValues("stack_inuse").Set(float64(m.StackInuse))
	
	// Collect goroutine count
	smc.metrics.activeConnections.WithLabelValues("goroutines").Set(float64(runtime.NumGoroutine()))
}
