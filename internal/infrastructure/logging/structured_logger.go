package logging

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"time"

	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
)

// LogConfig represents logging configuration
type LogConfig struct {
	Level      string `yaml:"level" json:"level"`
	Format     string `yaml:"format" json:"format"` // json or text
	Output     string `yaml:"output" json:"output"` // stdout, file, or both
	FilePath   string `yaml:"file_path" json:"file_path"`
	MaxSize    int    `yaml:"max_size" json:"max_size"`       // MB
	MaxBackups int    `yaml:"max_backups" json:"max_backups"` // number of backup files
	MaxAge     int    `yaml:"max_age" json:"max_age"`         // days
	Compress   bool   `yaml:"compress" json:"compress"`
}

// Logger interface defines logging methods
type Logger interface {
	Debug(ctx context.Context, msg string, fields ...interface{})
	Info(ctx context.Context, msg string, fields ...interface{})
	Warn(ctx context.Context, msg string, fields ...interface{})
	Error(ctx context.Context, msg string, fields ...interface{})
	Fatal(ctx context.Context, msg string, fields ...interface{})
	
	WithField(key string, value interface{}) Logger
	WithFields(fields map[string]interface{}) Logger
	WithError(err error) Logger
	WithContext(ctx context.Context) Logger
}

// StructuredLogger implements Logger interface using logrus
type StructuredLogger struct {
	logger *logrus.Logger
	entry  *logrus.Entry
}

// NewStructuredLogger creates a new structured logger
func NewStructuredLogger(config LogConfig) (*StructuredLogger, error) {
	logger := logrus.New()
	
	// Set log level
	level, err := logrus.ParseLevel(config.Level)
	if err != nil {
		return nil, fmt.Errorf("invalid log level: %w", err)
	}
	logger.SetLevel(level)
	
	// Set formatter
	switch config.Format {
	case "json":
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
			FieldMap: logrus.FieldMap{
				logrus.FieldKeyTime:  "timestamp",
				logrus.FieldKeyLevel: "level",
				logrus.FieldKeyMsg:   "message",
				logrus.FieldKeyFunc:  "caller",
			},
		})
	case "text":
		logger.SetFormatter(&logrus.TextFormatter{
			TimestampFormat: time.RFC3339,
			FullTimestamp:   true,
		})
	default:
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
		})
	}
	
	// Set output
	if err := setLogOutput(logger, config); err != nil {
		return nil, fmt.Errorf("failed to set log output: %w", err)
	}
	
	// Add hooks
	addLogHooks(logger)
	
	return &StructuredLogger{
		logger: logger,
		entry:  logger.WithFields(logrus.Fields{}),
	}, nil
}

// setLogOutput configures log output destinations
func setLogOutput(logger *logrus.Logger, config LogConfig) error {
	switch config.Output {
	case "stdout":
		logger.SetOutput(os.Stdout)
	case "file":
		if config.FilePath == "" {
			return fmt.Errorf("file path is required for file output")
		}
		
		// Create directory if it doesn't exist
		dir := filepath.Dir(config.FilePath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create log directory: %w", err)
		}
		
		// Use lumberjack for log rotation
		lumberjackLogger := &lumberjack.Logger{
			Filename:   config.FilePath,
			MaxSize:    config.MaxSize,
			MaxBackups: config.MaxBackups,
			MaxAge:     config.MaxAge,
			Compress:   config.Compress,
		}
		
		logger.SetOutput(lumberjackLogger)
	case "both":
		if config.FilePath == "" {
			return fmt.Errorf("file path is required for both output")
		}
		
		// Create directory if it doesn't exist
		dir := filepath.Dir(config.FilePath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create log directory: %w", err)
		}
		
		// Use lumberjack for log rotation
		lumberjackLogger := &lumberjack.Logger{
			Filename:   config.FilePath,
			MaxSize:    config.MaxSize,
			MaxBackups: config.MaxBackups,
			MaxAge:     config.MaxAge,
			Compress:   config.Compress,
		}
		
		// Create multi-writer for both stdout and file
		logger.SetOutput(io.MultiWriter(os.Stdout, lumberjackLogger))
	default:
		logger.SetOutput(os.Stdout)
	}
	
	return nil
}

// addLogHooks adds custom hooks to the logger
func addLogHooks(logger *logrus.Logger) {
	// Add caller hook to include file and line information
	logger.AddHook(&CallerHook{})
	
	// Add correlation ID hook
	logger.AddHook(&CorrelationIDHook{})
}

// Debug logs a debug message
func (sl *StructuredLogger) Debug(ctx context.Context, msg string, fields ...interface{}) {
	entry := sl.getEntryWithContext(ctx)
	entry = addFields(entry, fields...)
	entry.Debug(msg)
}

// Info logs an info message
func (sl *StructuredLogger) Info(ctx context.Context, msg string, fields ...interface{}) {
	entry := sl.getEntryWithContext(ctx)
	entry = addFields(entry, fields...)
	entry.Info(msg)
}

// Warn logs a warning message
func (sl *StructuredLogger) Warn(ctx context.Context, msg string, fields ...interface{}) {
	entry := sl.getEntryWithContext(ctx)
	entry = addFields(entry, fields...)
	entry.Warn(msg)
}

// Error logs an error message
func (sl *StructuredLogger) Error(ctx context.Context, msg string, fields ...interface{}) {
	entry := sl.getEntryWithContext(ctx)
	entry = addFields(entry, fields...)
	entry.Error(msg)
}

// Fatal logs a fatal message and exits
func (sl *StructuredLogger) Fatal(ctx context.Context, msg string, fields ...interface{}) {
	entry := sl.getEntryWithContext(ctx)
	entry = addFields(entry, fields...)
	entry.Fatal(msg)
}

// WithField adds a field to the logger
func (sl *StructuredLogger) WithField(key string, value interface{}) Logger {
	return &StructuredLogger{
		logger: sl.logger,
		entry:  sl.entry.WithField(key, value),
	}
}

// WithFields adds multiple fields to the logger
func (sl *StructuredLogger) WithFields(fields map[string]interface{}) Logger {
	return &StructuredLogger{
		logger: sl.logger,
		entry:  sl.entry.WithFields(fields),
	}
}

// WithError adds an error to the logger
func (sl *StructuredLogger) WithError(err error) Logger {
	return &StructuredLogger{
		logger: sl.logger,
		entry:  sl.entry.WithError(err),
	}
}

// WithContext adds context to the logger
func (sl *StructuredLogger) WithContext(ctx context.Context) Logger {
	return &StructuredLogger{
		logger: sl.logger,
		entry:  sl.getEntryWithContext(ctx),
	}
}

// getEntryWithContext extracts context information and adds it to the log entry
func (sl *StructuredLogger) getEntryWithContext(ctx context.Context) *logrus.Entry {
	entry := sl.entry
	
	// Extract correlation ID from context
	if correlationID := getCorrelationID(ctx); correlationID != "" {
		entry = entry.WithField("correlation_id", correlationID)
	}
	
	// Extract user ID from context
	if userID := getUserID(ctx); userID != "" {
		entry = entry.WithField("user_id", userID)
	}
	
	// Extract request ID from context
	if requestID := getRequestID(ctx); requestID != "" {
		entry = entry.WithField("request_id", requestID)
	}
	
	// Extract trace ID from context (for distributed tracing)
	if traceID := getTraceID(ctx); traceID != "" {
		entry = entry.WithField("trace_id", traceID)
	}
	
	return entry
}

// addFields adds key-value pairs to the log entry
func addFields(entry *logrus.Entry, fields ...interface{}) *logrus.Entry {
	if len(fields)%2 != 0 {
		entry = entry.WithField("invalid_fields", fields)
		return entry
	}
	
	logFields := make(logrus.Fields)
	for i := 0; i < len(fields); i += 2 {
		key, ok := fields[i].(string)
		if !ok {
			continue
		}
		logFields[key] = fields[i+1]
	}
	
	return entry.WithFields(logFields)
}

// Context key types
type contextKey string

const (
	CorrelationIDKey contextKey = "correlation_id"
	UserIDKey        contextKey = "user_id"
	RequestIDKey     contextKey = "request_id"
	TraceIDKey       contextKey = "trace_id"
)

// Context helper functions
func getCorrelationID(ctx context.Context) string {
	if id, ok := ctx.Value(CorrelationIDKey).(string); ok {
		return id
	}
	return ""
}

func getUserID(ctx context.Context) string {
	if id, ok := ctx.Value(UserIDKey).(string); ok {
		return id
	}
	return ""
}

func getRequestID(ctx context.Context) string {
	if id, ok := ctx.Value(RequestIDKey).(string); ok {
		return id
	}
	return ""
}

func getTraceID(ctx context.Context) string {
	if id, ok := ctx.Value(TraceIDKey).(string); ok {
		return id
	}
	return ""
}

// CallerHook adds caller information to log entries
type CallerHook struct{}

func (hook *CallerHook) Levels() []logrus.Level {
	return logrus.AllLevels
}

func (hook *CallerHook) Fire(entry *logrus.Entry) error {
	if pc, file, line, ok := runtime.Caller(8); ok {
		funcName := runtime.FuncForPC(pc).Name()
		entry.Data["caller"] = fmt.Sprintf("%s:%d", filepath.Base(file), line)
		entry.Data["function"] = filepath.Base(funcName)
	}
	return nil
}

// CorrelationIDHook adds correlation ID to log entries
type CorrelationIDHook struct{}

func (hook *CorrelationIDHook) Levels() []logrus.Level {
	return logrus.AllLevels
}

func (hook *CorrelationIDHook) Fire(entry *logrus.Entry) error {
	// This hook can be used to add correlation ID from context
	// if it's not already present
	return nil
}

// LoggerMiddleware creates a middleware for request logging
func LoggerMiddleware(logger Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()
			
			// Generate request ID
			requestID := generateRequestID()
			ctx := context.WithValue(r.Context(), RequestIDKey, requestID)
			r = r.WithContext(ctx)
			
			// Create response writer wrapper to capture status code
			wrapped := &responseWriter{ResponseWriter: w, statusCode: 200}
			
			// Process request
			next.ServeHTTP(wrapped, r)
			
			// Log request
			duration := time.Since(start)
			logger.Info(ctx, "HTTP request completed",
				"method", r.Method,
				"path", r.URL.Path,
				"status_code", wrapped.statusCode,
				"duration_ms", duration.Milliseconds(),
				"user_agent", r.UserAgent(),
				"remote_addr", r.RemoteAddr,
				"request_id", requestID,
			)
		})
	}
}

// responseWriter wraps http.ResponseWriter to capture status code
type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	// Simple implementation - in production, you might want to use UUID
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}

// Performance logging helpers
func LogDuration(logger Logger, ctx context.Context, operation string, start time.Time) {
	duration := time.Since(start)
	logger.Info(ctx, "Operation completed",
		"operation", operation,
		"duration_ms", duration.Milliseconds(),
	)
}

func LogSlowQuery(logger Logger, ctx context.Context, query string, duration time.Duration, threshold time.Duration) {
	if duration > threshold {
		logger.Warn(ctx, "Slow query detected",
			"query", query,
			"duration_ms", duration.Milliseconds(),
			"threshold_ms", threshold.Milliseconds(),
		)
	}
}
