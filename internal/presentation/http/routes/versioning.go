package routes

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"web-api/internal/infrastructure/container"
	"web-api/internal/shared/utils"

	"github.com/gin-gonic/gin"
)

// APIVersion represents an API version
type APIVersion struct {
	Major int
	Minor int
	Patch int
}

// String returns the string representation of the version
func (v APIVersion) String() string {
	return fmt.Sprintf("v%d.%d.%d", v.Major, v.Minor, v.Patch)
}

// ShortString returns the short version string (e.g., "v2")
func (v APIVersion) ShortString() string {
	return fmt.Sprintf("v%d", v.Major)
}

// ParseVersion parses a version string into APIVersion
func ParseVersion(version string) (APIVersion, error) {
	// Remove 'v' prefix if present
	version = strings.TrimPrefix(version, "v")
	
	parts := strings.Split(version, ".")
	if len(parts) < 1 || len(parts) > 3 {
		return APIVersion{}, fmt.Errorf("invalid version format: %s", version)
	}
	
	major, err := strconv.Atoi(parts[0])
	if err != nil {
		return APIVersion{}, fmt.Errorf("invalid major version: %s", parts[0])
	}
	
	minor := 0
	if len(parts) > 1 {
		minor, err = strconv.Atoi(parts[1])
		if err != nil {
			return APIVersion{}, fmt.Errorf("invalid minor version: %s", parts[1])
		}
	}
	
	patch := 0
	if len(parts) > 2 {
		patch, err = strconv.Atoi(parts[2])
		if err != nil {
			return APIVersion{}, fmt.Errorf("invalid patch version: %s", parts[2])
		}
	}
	
	return APIVersion{Major: major, Minor: minor, Patch: patch}, nil
}

// VersionConfig represents version configuration
type VersionConfig struct {
	CurrentVersion    APIVersion
	SupportedVersions []APIVersion
	DefaultVersion    APIVersion
	DeprecatedVersions map[string]string // version -> deprecation message
}

// VersionManager manages API versioning
type VersionManager struct {
	config VersionConfig
}

// NewVersionManager creates a new version manager
func NewVersionManager() *VersionManager {
	return &VersionManager{
		config: VersionConfig{
			CurrentVersion: APIVersion{Major: 2, Minor: 0, Patch: 0},
			SupportedVersions: []APIVersion{
				{Major: 1, Minor: 0, Patch: 0},
				{Major: 2, Minor: 0, Patch: 0},
			},
			DefaultVersion: APIVersion{Major: 2, Minor: 0, Patch: 0},
			DeprecatedVersions: map[string]string{
				"v1": "API v1 is deprecated and will be removed in v3. Please migrate to v2.",
			},
		},
	}
}

// VersionMiddleware creates middleware for API versioning
func (vm *VersionManager) VersionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		version := vm.extractVersion(c)
		
		// Validate version
		if !vm.isVersionSupported(version) {
			utils.RespondWithBadRequest(c, fmt.Sprintf("Unsupported API version: %s", version.ShortString()))
			c.Abort()
			return
		}
		
		// Add deprecation warning if applicable
		if deprecationMsg, isDeprecated := vm.config.DeprecatedVersions[version.ShortString()]; isDeprecated {
			c.Header("X-API-Deprecation-Warning", deprecationMsg)
			c.Header("X-API-Sunset-Date", "2024-12-31") // Example sunset date
		}
		
		// Store version in context
		c.Set("api_version", version)
		c.Set("api_version_string", version.ShortString())
		
		// Add version headers
		c.Header("X-API-Version", version.String())
		c.Header("X-API-Current-Version", vm.config.CurrentVersion.String())
		
		c.Next()
	}
}

// extractVersion extracts API version from request
func (vm *VersionManager) extractVersion(c *gin.Context) APIVersion {
	// Try to get version from URL path first (e.g., /api/v2/...)
	path := c.Request.URL.Path
	if strings.HasPrefix(path, "/api/v") {
		parts := strings.Split(path, "/")
		if len(parts) >= 3 {
			if version, err := ParseVersion(parts[2]); err == nil {
				return version
			}
		}
	}
	
	// Try to get version from Accept header (e.g., application/vnd.api+json;version=2)
	accept := c.GetHeader("Accept")
	if strings.Contains(accept, "version=") {
		parts := strings.Split(accept, "version=")
		if len(parts) > 1 {
			versionStr := strings.Split(parts[1], ";")[0]
			versionStr = strings.Split(versionStr, ",")[0]
			if version, err := ParseVersion(versionStr); err == nil {
				return version
			}
		}
	}
	
	// Try to get version from custom header
	versionHeader := c.GetHeader("X-API-Version")
	if versionHeader != "" {
		if version, err := ParseVersion(versionHeader); err == nil {
			return version
		}
	}
	
	// Try to get version from query parameter
	versionQuery := c.Query("version")
	if versionQuery != "" {
		if version, err := ParseVersion(versionQuery); err == nil {
			return version
		}
	}
	
	// Return default version
	return vm.config.DefaultVersion
}

// isVersionSupported checks if a version is supported
func (vm *VersionManager) isVersionSupported(version APIVersion) bool {
	for _, supported := range vm.config.SupportedVersions {
		if version.Major == supported.Major {
			return true
		}
	}
	return false
}

// GetCurrentVersion returns the current API version
func (vm *VersionManager) GetCurrentVersion() APIVersion {
	return vm.config.CurrentVersion
}

// GetSupportedVersions returns all supported versions
func (vm *VersionManager) GetSupportedVersions() []APIVersion {
	return vm.config.SupportedVersions
}

// VersionedRouter manages versioned routes
type VersionedRouter struct {
	versionManager *VersionManager
	container      *container.Container
}

// NewVersionedRouter creates a new versioned router
func NewVersionedRouter(container *container.Container) *VersionedRouter {
	return &VersionedRouter{
		versionManager: NewVersionManager(),
		container:      container,
	}
}

// SetupRoutes sets up all versioned routes
func (vr *VersionedRouter) SetupRoutes(router *gin.Engine) {
	// Add version middleware
	router.Use(vr.versionManager.VersionMiddleware())
	
	// API info endpoint (unversioned)
	router.GET("/api", vr.getAPIInfo)
	
	// Version-specific route groups
	v1Group := router.Group("/api/v1")
	vr.setupV1Routes(v1Group)
	
	v2Group := router.Group("/api/v2")
	vr.setupV2Routes(v2Group)
	
	// Default routes (latest version)
	defaultGroup := router.Group("/api")
	vr.setupV2Routes(defaultGroup) // v2 is current
}

// setupV1Routes sets up v1 API routes
func (vr *VersionedRouter) setupV1Routes(group *gin.RouterGroup) {
	// Legacy v1 routes with backward compatibility
	group.Use(vr.v1CompatibilityMiddleware())
	
	// Key Model Records (v1 format)
	keyModelGroup := group.Group("/key-model-records")
	{
		keyModelGroup.GET("", vr.container.KeyModelHandler.ListV1)
		keyModelGroup.POST("", vr.container.KeyModelHandler.CreateV1)
		keyModelGroup.GET("/:id", vr.container.KeyModelHandler.GetByIDV1)
		keyModelGroup.PUT("/:id", vr.container.KeyModelHandler.UpdateV1)
		keyModelGroup.DELETE("/:id", vr.container.KeyModelHandler.DeleteV1)
	}
	
	// Lookup tables
	group.GET("/seasons", vr.container.LookupHandler.GetSeasons)
	group.GET("/factories", vr.container.LookupHandler.GetFactories)
	group.GET("/key-model-types", vr.container.LookupHandler.GetKeyModelTypes)
}

// setupV2Routes sets up v2 API routes
func (vr *VersionedRouter) setupV2Routes(group *gin.RouterGroup) {
	// Key Model Records (v2 format with enhanced features)
	keyModelGroup := group.Group("/key-model-records")
	{
		keyModelGroup.GET("", vr.container.KeyModelHandler.List)
		keyModelGroup.POST("", vr.container.KeyModelHandler.Create)
		keyModelGroup.GET("/:id", vr.container.KeyModelHandler.GetByID)
		keyModelGroup.PUT("/:id", vr.container.KeyModelHandler.Update)
		keyModelGroup.DELETE("/:id", vr.container.KeyModelHandler.Delete)
		
		// v2 specific endpoints
		keyModelGroup.GET("/search", vr.container.KeyModelHandler.Search)
		keyModelGroup.POST("/bulk", vr.container.KeyModelHandler.BulkCreate)
		keyModelGroup.PUT("/bulk", vr.container.KeyModelHandler.BulkUpdate)
		keyModelGroup.DELETE("/bulk", vr.container.KeyModelHandler.BulkDelete)
		
		// Stage management
		stageGroup := keyModelGroup.Group("/:id/stages")
		{
			stageGroup.POST("/:stageType/advance", vr.container.KeyModelHandler.AdvanceStage)
			stageGroup.PUT("/:stageType", vr.container.KeyModelHandler.UpdateStage)
			stageGroup.POST("/:stageType/complete", vr.container.KeyModelHandler.CompleteStage)
		}
	}
	
	// Enhanced lookup tables with CRUD operations
	lookupGroup := group.Group("/lookup")
	{
		// Seasons
		seasonGroup := lookupGroup.Group("/seasons")
		{
			seasonGroup.GET("", vr.container.LookupHandler.GetSeasons)
			seasonGroup.POST("", vr.container.LookupHandler.CreateSeason)
			seasonGroup.PUT("/:id", vr.container.LookupHandler.UpdateSeason)
			seasonGroup.DELETE("/:id", vr.container.LookupHandler.DeleteSeason)
		}
		
		// Factories
		factoryGroup := lookupGroup.Group("/factories")
		{
			factoryGroup.GET("", vr.container.LookupHandler.GetFactories)
			factoryGroup.POST("", vr.container.LookupHandler.CreateFactory)
			factoryGroup.PUT("/:id", vr.container.LookupHandler.UpdateFactory)
			factoryGroup.DELETE("/:id", vr.container.LookupHandler.DeleteFactory)
		}
		
		// Key Model Types
		typeGroup := lookupGroup.Group("/key-model-types")
		{
			typeGroup.GET("", vr.container.LookupHandler.GetKeyModelTypes)
			typeGroup.POST("", vr.container.LookupHandler.CreateKeyModelType)
			typeGroup.PUT("/:id", vr.container.LookupHandler.UpdateKeyModelType)
			typeGroup.DELETE("/:id", vr.container.LookupHandler.DeleteKeyModelType)
		}
	}
	
	// Authentication (v2 with enhanced security)
	authGroup := group.Group("/auth")
	{
		authGroup.POST("/login", vr.container.AuthHandler.Login)
		authGroup.POST("/refresh", vr.container.AuthHandler.RefreshToken)
		authGroup.POST("/logout", vr.container.AuthHandler.Logout)
		authGroup.POST("/revoke", vr.container.AuthHandler.RevokeToken)
	}
	
	// Analytics and reporting (v2 only)
	analyticsGroup := group.Group("/analytics")
	{
		analyticsGroup.GET("/dashboard", vr.container.AnalyticsHandler.GetDashboard)
		analyticsGroup.GET("/reports/stage-progress", vr.container.AnalyticsHandler.GetStageProgressReport)
		analyticsGroup.GET("/reports/factory-performance", vr.container.AnalyticsHandler.GetFactoryPerformanceReport)
		analyticsGroup.GET("/reports/seasonal-trends", vr.container.AnalyticsHandler.GetSeasonalTrendsReport)
	}
}

// v1CompatibilityMiddleware provides backward compatibility for v1 API
func (vr *VersionedRouter) v1CompatibilityMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Add v1 compatibility headers
		c.Header("X-API-Compatibility", "v1")
		
		// Transform v1 requests to v2 format if needed
		// This middleware can handle request/response transformation
		
		c.Next()
		
		// Transform v2 responses back to v1 format if needed
		vr.transformResponseForV1(c)
	}
}

// transformResponseForV1 transforms v2 responses to v1 format
func (vr *VersionedRouter) transformResponseForV1(c *gin.Context) {
	// Implementation would depend on specific differences between v1 and v2
	// For example, removing new fields, renaming fields, etc.
}

// getAPIInfo returns API information
func (vr *VersionedRouter) getAPIInfo(c *gin.Context) {
	info := map[string]interface{}{
		"name":        "Key Model Tracking API",
		"description": "API for tracking key model development through various stages",
		"version":     vr.versionManager.GetCurrentVersion().String(),
		"supportedVersions": func() []string {
			versions := make([]string, len(vr.versionManager.GetSupportedVersions()))
			for i, v := range vr.versionManager.GetSupportedVersions() {
				versions[i] = v.ShortString()
			}
			return versions
		}(),
		"deprecatedVersions": vr.versionManager.config.DeprecatedVersions,
		"endpoints": map[string]string{
			"health":      "/health",
			"metrics":     "/metrics",
			"docs":        "/docs",
			"openapi":     "/docs/openapi.yaml",
		},
		"documentation": "https://api-docs.company.com",
		"support":       "<EMAIL>",
	}
	
	utils.RespondWithSuccess(c, info, "API information retrieved successfully")
}

// ContentNegotiationMiddleware handles content negotiation for different versions
func ContentNegotiationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		accept := c.GetHeader("Accept")
		
		// Handle version-specific content types
		switch {
		case strings.Contains(accept, "application/vnd.api.v1+json"):
			c.Set("response_format", "v1")
		case strings.Contains(accept, "application/vnd.api.v2+json"):
			c.Set("response_format", "v2")
		case strings.Contains(accept, "application/json"):
			// Default to latest version
			c.Set("response_format", "v2")
		default:
			c.Set("response_format", "v2")
		}
		
		c.Next()
	}
}

// GetVersionFromContext extracts API version from gin context
func GetVersionFromContext(c *gin.Context) APIVersion {
	if version, exists := c.Get("api_version"); exists {
		if v, ok := version.(APIVersion); ok {
			return v
		}
	}
	return APIVersion{Major: 2, Minor: 0, Patch: 0} // Default
}

// IsVersionAtLeast checks if the current version is at least the specified version
func IsVersionAtLeast(c *gin.Context, major, minor int) bool {
	version := GetVersionFromContext(c)
	return version.Major > major || (version.Major == major && version.Minor >= minor)
}
