package middleware

import (
	"context"
	"net/http"
	"strings"

	"web-api/internal/shared/errors"
	"web-api/internal/shared/utils"
	"web-api/pkg/security"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware provides authentication functionality
type AuthMiddleware struct {
	jwtManager *security.JWTManagerWithBlacklist
}

// NewAuthMiddleware creates a new authentication middleware
func NewAuthMiddleware(jwtManager *security.JWTManagerWithBlacklist) *AuthMiddleware {
	return &AuthMiddleware{
		jwtManager: jwtManager,
	}
}

// RequireAuth middleware that requires authentication
func (am *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token, err := am.extractToken(c)
		if err != nil {
			utils.RespondWithUnauthorized(c, err.Error())
			c.Abort()
			return
		}

		claims, err := am.jwtManager.ValidateTokenWithBlacklist(token)
		if err != nil {
			utils.RespondWithUnauthorized(c, "Invalid or expired token")
			c.Abort()
			return
		}

		// Store user information in context
		am.setUserContext(c, claims)
		c.Next()
	}
}

// OptionalAuth middleware that optionally authenticates users
func (am *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token, err := am.extractToken(c)
		if err != nil {
			// No token provided, continue without authentication
			c.Next()
			return
		}

		claims, err := am.jwtManager.ValidateTokenWithBlacklist(token)
		if err != nil {
			// Invalid token, continue without authentication
			c.Next()
			return
		}

		// Store user information in context
		am.setUserContext(c, claims)
		c.Next()
	}
}

// RequireRole middleware that requires specific roles
func (am *AuthMiddleware) RequireRole(requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userClaims := am.getUserFromContext(c)
		if userClaims == nil {
			utils.RespondWithUnauthorized(c, "Authentication required")
			c.Abort()
			return
		}

		if !am.hasAnyRole(userClaims.Roles, requiredRoles) {
			utils.RespondWithForbidden(c, "Insufficient permissions")
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequirePermission middleware that requires specific permissions
func (am *AuthMiddleware) RequirePermission(requiredPermissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userClaims := am.getUserFromContext(c)
		if userClaims == nil {
			utils.RespondWithUnauthorized(c, "Authentication required")
			c.Abort()
			return
		}

		if !am.hasAnyPermission(userClaims.Permissions, requiredPermissions) {
			utils.RespondWithForbidden(c, "Insufficient permissions")
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireOwnership middleware that requires resource ownership
func (am *AuthMiddleware) RequireOwnership(resourceUserIDParam string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userClaims := am.getUserFromContext(c)
		if userClaims == nil {
			utils.RespondWithUnauthorized(c, "Authentication required")
			c.Abort()
			return
		}

		resourceUserID := c.Param(resourceUserIDParam)
		if resourceUserID == "" {
			resourceUserID = c.Query(resourceUserIDParam)
		}

		if resourceUserID != userClaims.UserID && !am.hasAdminRole(userClaims.Roles) {
			utils.RespondWithForbidden(c, "Access denied: resource ownership required")
			c.Abort()
			return
		}

		c.Next()
	}
}

// RateLimitByUser middleware that applies rate limiting per user
func (am *AuthMiddleware) RateLimitByUser() gin.HandlerFunc {
	return func(c *gin.Context) {
		userClaims := am.getUserFromContext(c)
		if userClaims != nil {
			// Apply user-specific rate limiting
			c.Set("rate_limit_key", "user:"+userClaims.UserID)
		} else {
			// Apply IP-based rate limiting for unauthenticated users
			c.Set("rate_limit_key", "ip:"+c.ClientIP())
		}
		c.Next()
	}
}

// extractToken extracts JWT token from request
func (am *AuthMiddleware) extractToken(c *gin.Context) (string, error) {
	// Try Authorization header first
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		return security.ExtractTokenFromHeader(authHeader)
	}

	// Try query parameter
	token := c.Query("token")
	if token != "" {
		return token, nil
	}

	// Try cookie
	cookie, err := c.Cookie("access_token")
	if err == nil && cookie != "" {
		return cookie, nil
	}

	return "", errors.NewUnauthorizedError("No authentication token provided")
}

// setUserContext stores user information in the request context
func (am *AuthMiddleware) setUserContext(c *gin.Context, claims *security.JWTClaims) {
	c.Set("user_id", claims.UserID)
	c.Set("username", claims.Username)
	c.Set("email", claims.Email)
	c.Set("roles", claims.Roles)
	c.Set("permissions", claims.Permissions)
	c.Set("session_id", claims.SessionID)
	c.Set("user_claims", claims)
}

// getUserFromContext retrieves user claims from context
func (am *AuthMiddleware) getUserFromContext(c *gin.Context) *security.JWTClaims {
	claims, exists := c.Get("user_claims")
	if !exists {
		return nil
	}
	
	userClaims, ok := claims.(*security.JWTClaims)
	if !ok {
		return nil
	}
	
	return userClaims
}

// hasAnyRole checks if user has any of the required roles
func (am *AuthMiddleware) hasAnyRole(userRoles, requiredRoles []string) bool {
	for _, userRole := range userRoles {
		for _, requiredRole := range requiredRoles {
			if userRole == requiredRole {
				return true
			}
		}
	}
	return false
}

// hasAnyPermission checks if user has any of the required permissions
func (am *AuthMiddleware) hasAnyPermission(userPermissions, requiredPermissions []string) bool {
	for _, userPermission := range userPermissions {
		for _, requiredPermission := range requiredPermissions {
			if userPermission == requiredPermission {
				return true
			}
		}
	}
	return false
}

// hasAdminRole checks if user has admin role
func (am *AuthMiddleware) hasAdminRole(userRoles []string) bool {
	adminRoles := []string{"admin", "super_admin", "system_admin"}
	return am.hasAnyRole(userRoles, adminRoles)
}

// GetCurrentUser returns the current authenticated user
func GetCurrentUser(c *gin.Context) *security.JWTClaims {
	claims, exists := c.Get("user_claims")
	if !exists {
		return nil
	}
	
	userClaims, ok := claims.(*security.JWTClaims)
	if !ok {
		return nil
	}
	
	return userClaims
}

// GetCurrentUserID returns the current user ID
func GetCurrentUserID(c *gin.Context) string {
	userID, exists := c.Get("user_id")
	if !exists {
		return ""
	}
	
	id, ok := userID.(string)
	if !ok {
		return ""
	}
	
	return id
}

// IsAuthenticated checks if the current request is authenticated
func IsAuthenticated(c *gin.Context) bool {
	_, exists := c.Get("user_claims")
	return exists
}

// HasRole checks if the current user has a specific role
func HasRole(c *gin.Context, role string) bool {
	roles, exists := c.Get("roles")
	if !exists {
		return false
	}
	
	userRoles, ok := roles.([]string)
	if !ok {
		return false
	}
	
	for _, userRole := range userRoles {
		if userRole == role {
			return true
		}
	}
	return false
}

// HasPermission checks if the current user has a specific permission
func HasPermission(c *gin.Context, permission string) bool {
	permissions, exists := c.Get("permissions")
	if !exists {
		return false
	}
	
	userPermissions, ok := permissions.([]string)
	if !ok {
		return false
	}
	
	for _, userPermission := range userPermissions {
		if userPermission == permission {
			return true
		}
	}
	return false
}

// APIKeyMiddleware provides API key authentication
type APIKeyMiddleware struct {
	validAPIKeys map[string]APIKeyInfo
}

// APIKeyInfo represents API key information
type APIKeyInfo struct {
	Name        string
	Permissions []string
	RateLimit   int
}

// NewAPIKeyMiddleware creates a new API key middleware
func NewAPIKeyMiddleware(apiKeys map[string]APIKeyInfo) *APIKeyMiddleware {
	return &APIKeyMiddleware{
		validAPIKeys: apiKeys,
	}
}

// RequireAPIKey middleware that requires valid API key
func (akm *APIKeyMiddleware) RequireAPIKey() gin.HandlerFunc {
	return func(c *gin.Context) {
		apiKey := c.GetHeader("X-API-Key")
		if apiKey == "" {
			apiKey = c.Query("api_key")
		}

		if apiKey == "" {
			utils.RespondWithUnauthorized(c, "API key is required")
			c.Abort()
			return
		}

		keyInfo, exists := akm.validAPIKeys[apiKey]
		if !exists {
			utils.RespondWithUnauthorized(c, "Invalid API key")
			c.Abort()
			return
		}

		// Store API key information in context
		c.Set("api_key_name", keyInfo.Name)
		c.Set("api_key_permissions", keyInfo.Permissions)
		c.Set("api_key_rate_limit", keyInfo.RateLimit)
		c.Next()
	}
}

// CORSMiddleware provides CORS support
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		
		// Allow specific origins or all origins in development
		allowedOrigins := []string{
			"http://localhost:3000",
			"http://localhost:8080",
			"https://your-frontend-domain.com",
		}
		
		isAllowed := false
		for _, allowedOrigin := range allowedOrigins {
			if origin == allowedOrigin {
				isAllowed = true
				break
			}
		}
		
		if isAllowed {
			c.Header("Access-Control-Allow-Origin", origin)
		}
		
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-API-Key")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}
