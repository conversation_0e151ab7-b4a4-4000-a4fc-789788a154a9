package middleware

import (
	"fmt"
	"net/http"
	"reflect"
	"strings"

	"web-api/internal/shared/errors"
	"web-api/internal/shared/utils"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

// ValidationMiddleware provides request validation functionality
type ValidationMiddleware struct {
	validator *validator.Validate
}

// NewValidationMiddleware creates a new validation middleware
func NewValidationMiddleware() *ValidationMiddleware {
	v := validator.New()
	
	// Register custom validators
	v.RegisterValidation("uuid", validateUUID)
	v.RegisterValidation("stage_type", validateStageType)
	v.RegisterValidation("factory_code", validateFactoryCode)
	v.RegisterValidation("season_code", validateSeasonCode)
	
	// Register tag name function for better error messages
	v.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})
	
	return &ValidationMiddleware{
		validator: v,
	}
}

// ValidateJSON validates JSON request body
func (vm *ValidationMiddleware) ValidateJSON(model interface{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := c.ShouldBindJSON(model); err != nil {
			vm.handleValidationError(c, err)
			return
		}
		
		if err := vm.validator.Struct(model); err != nil {
			vm.handleValidationError(c, err)
			return
		}
		
		// Store validated model in context
		c.Set("validated_model", model)
		c.Next()
	}
}

// ValidateQuery validates query parameters
func (vm *ValidationMiddleware) ValidateQuery(model interface{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := c.ShouldBindQuery(model); err != nil {
			vm.handleValidationError(c, err)
			return
		}
		
		if err := vm.validator.Struct(model); err != nil {
			vm.handleValidationError(c, err)
			return
		}
		
		c.Set("validated_query", model)
		c.Next()
	}
}

// ValidateParams validates URL parameters
func (vm *ValidationMiddleware) ValidateParams(model interface{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := c.ShouldBindUri(model); err != nil {
			vm.handleValidationError(c, err)
			return
		}
		
		if err := vm.validator.Struct(model); err != nil {
			vm.handleValidationError(c, err)
			return
		}
		
		c.Set("validated_params", model)
		c.Next()
	}
}

// ValidateMultipart validates multipart form data
func (vm *ValidationMiddleware) ValidateMultipart(model interface{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := c.ShouldBind(model); err != nil {
			vm.handleValidationError(c, err)
			return
		}
		
		if err := vm.validator.Struct(model); err != nil {
			vm.handleValidationError(c, err)
			return
		}
		
		c.Set("validated_form", model)
		c.Next()
	}
}

// handleValidationError processes validation errors and returns appropriate response
func (vm *ValidationMiddleware) handleValidationError(c *gin.Context, err error) {
	var appError *errors.AppError
	
	switch e := err.(type) {
	case validator.ValidationErrors:
		appError = vm.convertValidationErrors(e)
	case *validator.InvalidValidationError:
		appError = errors.NewValidationError("invalid validation configuration")
	default:
		appError = errors.NewValidationError("invalid request format: " + err.Error())
	}
	
	utils.RespondWithError(c, appError)
}

// convertValidationErrors converts validator errors to AppError
func (vm *ValidationMiddleware) convertValidationErrors(validationErrors validator.ValidationErrors) *errors.AppError {
	var fieldErrors []errors.FieldError
	
	for _, err := range validationErrors {
		fieldError := errors.FieldError{
			Field:   err.Field(),
			Message: vm.getErrorMessage(err),
			Value:   fmt.Sprintf("%v", err.Value()),
		}
		fieldErrors = append(fieldErrors, fieldError)
	}
	
	return errors.NewValidationErrors(fieldErrors)
}

// getErrorMessage returns a user-friendly error message for validation errors
func (vm *ValidationMiddleware) getErrorMessage(err validator.FieldError) string {
	field := err.Field()
	tag := err.Tag()
	param := err.Param()
	
	switch tag {
	case "required":
		return fmt.Sprintf("%s is required", field)
	case "min":
		return fmt.Sprintf("%s must be at least %s characters long", field, param)
	case "max":
		return fmt.Sprintf("%s must be at most %s characters long", field, param)
	case "email":
		return fmt.Sprintf("%s must be a valid email address", field)
	case "uuid":
		return fmt.Sprintf("%s must be a valid UUID", field)
	case "oneof":
		return fmt.Sprintf("%s must be one of: %s", field, param)
	case "stage_type":
		return fmt.Sprintf("%s must be a valid stage type", field)
	case "factory_code":
		return fmt.Sprintf("%s must be a valid factory code", field)
	case "season_code":
		return fmt.Sprintf("%s must be a valid season code", field)
	case "gte":
		return fmt.Sprintf("%s must be greater than or equal to %s", field, param)
	case "lte":
		return fmt.Sprintf("%s must be less than or equal to %s", field, param)
	case "len":
		return fmt.Sprintf("%s must be exactly %s characters long", field, param)
	case "numeric":
		return fmt.Sprintf("%s must be a number", field)
	case "alpha":
		return fmt.Sprintf("%s must contain only letters", field)
	case "alphanum":
		return fmt.Sprintf("%s must contain only letters and numbers", field)
	case "url":
		return fmt.Sprintf("%s must be a valid URL", field)
	case "datetime":
		return fmt.Sprintf("%s must be a valid datetime", field)
	default:
		return fmt.Sprintf("%s is invalid", field)
	}
}

// Custom validation functions

// validateUUID validates UUID format
func validateUUID(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	if value == "" {
		return true // Let required tag handle empty values
	}
	
	// Simple UUID format validation
	return len(value) == 36 && strings.Count(value, "-") == 4
}

// validateStageType validates stage type values
func validateStageType(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	validStages := []string{
		"PRODUCT_CREATION",
		"COMMERCIAL_EST",
		"COMMERCIAL_FST",
		"MANUFACTURING",
		"COMPLETED",
	}
	
	for _, stage := range validStages {
		if value == stage {
			return true
		}
	}
	return false
}

// validateFactoryCode validates factory code format
func validateFactoryCode(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	if value == "" {
		return true
	}
	
	// Factory code should be 2-5 uppercase letters
	return len(value) >= 2 && len(value) <= 5 && strings.ToUpper(value) == value
}

// validateSeasonCode validates season code format
func validateSeasonCode(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	if value == "" {
		return true
	}
	
	// Season code should be like S25, F24, W25, etc.
	if len(value) != 3 {
		return false
	}
	
	firstChar := value[0]
	if firstChar != 'S' && firstChar != 'F' && firstChar != 'W' && firstChar != 'H' {
		return false
	}
	
	// Check if last two characters are digits
	for i := 1; i < 3; i++ {
		if value[i] < '0' || value[i] > '9' {
			return false
		}
	}
	
	return true
}

// BusinessValidationMiddleware provides business rule validation
type BusinessValidationMiddleware struct {
	// Add business validation dependencies here
}

// NewBusinessValidationMiddleware creates a new business validation middleware
func NewBusinessValidationMiddleware() *BusinessValidationMiddleware {
	return &BusinessValidationMiddleware{}
}

// ValidateKeyModelCreation validates business rules for key model creation
func (bvm *BusinessValidationMiddleware) ValidateKeyModelCreation() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get validated model from context
		model, exists := c.Get("validated_model")
		if !exists {
			utils.RespondWithError(c, errors.NewInternalError("validation model not found", nil))
			return
		}
		
		// Perform business validation
		if err := bvm.validateKeyModelBusinessRules(model); err != nil {
			utils.RespondWithError(c, err)
			return
		}
		
		c.Next()
	}
}

// validateKeyModelBusinessRules validates business rules for key model
func (bvm *BusinessValidationMiddleware) validateKeyModelBusinessRules(model interface{}) *errors.AppError {
	// Add business validation logic here
	// For example:
	// - Check if colorway + material combination already exists
	// - Validate season is not in the past
	// - Check factory capacity
	// - Validate material availability
	
	return nil
}

// SanitizationMiddleware provides input sanitization
type SanitizationMiddleware struct{}

// NewSanitizationMiddleware creates a new sanitization middleware
func NewSanitizationMiddleware() *SanitizationMiddleware {
	return &SanitizationMiddleware{}
}

// SanitizeInput sanitizes input data
func (sm *SanitizationMiddleware) SanitizeInput() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Sanitize headers
		sm.sanitizeHeaders(c)
		
		// Sanitize query parameters
		sm.sanitizeQueryParams(c)
		
		c.Next()
	}
}

// sanitizeHeaders sanitizes HTTP headers
func (sm *SanitizationMiddleware) sanitizeHeaders(c *gin.Context) {
	// Remove potentially dangerous headers
	dangerousHeaders := []string{
		"X-Forwarded-Host",
		"X-Original-URL",
		"X-Rewrite-URL",
	}
	
	for _, header := range dangerousHeaders {
		c.Request.Header.Del(header)
	}
}

// sanitizeQueryParams sanitizes query parameters
func (sm *SanitizationMiddleware) sanitizeQueryParams(c *gin.Context) {
	// Add query parameter sanitization logic here
	// For example, remove SQL injection patterns, XSS patterns, etc.
}
