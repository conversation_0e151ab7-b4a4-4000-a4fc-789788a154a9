package request

import (
	"web-api/internal/pkg/models/types"
)

// KeyModelRecordRequest represents the request for creating/updating key model records
type KeyModelRecordRequest struct {
	// Basic Information
	ColorwayID     string              `json:"colorwayId" `
	MaterialID     string              `json:"materialId" `
	KeyModelType   string              `json:"keyModelType"`
	ProductName    string              `json:"productName" `
	FTY            string              `json:"fty" `
	Quantity       *int                `json:"quantity"`
	ISD            string              `json:"isd" `
	Season         string              `json:"season" `
	POReceivedDate *types.FlexibleTime `json:"poReceivedDate"`
	CreatedBy      *string             `json:"createdBy"`
	UpdatedBy      *string             `json:"updatedBy"`

	// Product Creation Stage (GTMF RFC UPDATE)
	UpdateDate       *types.FlexibleTime `json:"updateDate"`
	POReceived       *bool               `json:"poReceived"`
	PFCValidated     *bool               `json:"pfcValidated"`
	PTRSSCFMed       *bool               `json:"ptrssCfmed"`
	DesignKeyFeature *string             `json:"designKeyFeature"`
	DevLearning      *string             `json:"devLearning"`
	HRMaterialUpdate *string             `json:"hrMaterialUpdate"`
	GTMComment       *string             `json:"gtmComment"`

	// Commercial Stage (EST UPDATE)
	ESTDate         *types.FlexibleTime `json:"estDate"`
	FirstESTPass    *bool               `json:"firstEstPass"`
	SecondESTPass   *bool               `json:"secondEstPass"`
	FittingApproval *bool               `json:"fittingApproval"`
	PTRSSTested     *bool               `json:"ptrssTestedEst"`
	ESTComment      *string             `json:"estComment"`

	// Commercial Stage (FST UPDATE)
	SampleReceivedDate *types.FlexibleTime `json:"sampleReceivedDate"`
	FSTDate            *types.FlexibleTime `json:"fstDate"`
	FirstFSTPass       *bool               `json:"firstFstPass"`
	SecondFSTPass      *bool               `json:"secondFstPass"`
	PTRSSTestedFST     *bool               `json:"ptrssTestedFst"`
	PFCCFMed           *bool               `json:"pfcCfmed"`
	ToolingCFMed       *bool               `json:"toolingCfmed"`
	FSTComment         *string             `json:"fstComment"`

	// Manufacturing Stage (PROD MATERIAL UPDATE)
	ProdMaterialUpdateDate *types.FlexibleTime `json:"prodMaterialUpdateDate"`
	PhysicalTestPass       *bool               `json:"physicalTestPass"`
	VisualCheckPass        *bool               `json:"visualCheckPass"`
	ProdMaterialComment    *string             `json:"prodMaterialComment"`

	// Manufacturing Stage (MPPA UPDATE - Process Assessment)
	ProcessAssessmentDate *types.FlexibleTime `json:"processAssessmentDate"`
	MPPACheckProcess      *bool               `json:"mppaCheckProcess"`
	MPPAPassProcess       *bool               `json:"mppaPassProcess"`
	ProcessComment        *string             `json:"processComment"`

	// Manufacturing Stage (MPPA UPDATE - Product Assessment)
	ProductAssessmentDate *types.FlexibleTime `json:"productAssessmentDate"`
	MPPACheckProduct      *bool               `json:"mppaCheckProduct"`
	MPPAPassProduct       *bool               `json:"mppaPassProduct"`
	ProductComment        *string             `json:"productComment"`

	// User tracking
	UserID *string `json:"userId"`
}

// KeyModelRecordUpdateRequest represents the request for updating key model records (all fields optional)
type KeyModelRecordUpdateRequest struct {
	// Basic Information
	ColorwayID     *string             `json:"colorwayId"`
	MaterialID     *string             `json:"materialId"`
	KeyModelType   *string             `json:"keyModelType"`
	ProductName    *string             `json:"productName"`
	FTY            *string             `json:"fty"`
	Quantity       *int                `json:"quantity"`
	ISD            *string             `json:"isd"`
	Season         *string             `json:"season"`
	POReceivedDate *types.FlexibleTime `json:"poReceivedDate"`
	UpdatedBy      *string             `json:"updatedBy"`
	IsActive       *bool               `json:"isActive"`

	// Product Creation Stage (GTMF RFC UPDATE)
	UpdateDate       *types.FlexibleTime `json:"updateDate"`
	POReceived       *bool               `json:"poReceived"`
	PFCValidated     *bool               `json:"pfcValidated"`
	PTRSSCFMed       *bool               `json:"ptrssCfmed"`
	DesignKeyFeature *string             `json:"designKeyFeature"`
	DevLearning      *string             `json:"devLearning"`
	HRMaterialUpdate *string             `json:"hrMaterialUpdate"`
	GTMComment       *string             `json:"gtmComment"`

	// Commercial Stage (EST UPDATE)
	ESTDate         *types.FlexibleTime `json:"estDate"`
	FirstESTPass    *bool               `json:"firstEstPass"`
	SecondESTPass   *bool               `json:"secondEstPass"`
	FittingApproval *bool               `json:"fittingApproval"`
	PTRSSTested     *bool               `json:"ptrssTestedEst"`
	ESTComment      *string             `json:"estComment"`

	// Commercial Stage (FST UPDATE)
	SampleReceivedDate *types.FlexibleTime `json:"sampleReceivedDate"`
	FSTDate            *types.FlexibleTime `json:"fstDate"`
	FirstFSTPass       *bool               `json:"firstFstPass"`
	SecondFSTPass      *bool               `json:"secondFstPass"`
	PTRSSTestedFST     *bool               `json:"ptrssTestedFst"`
	PFCCFMed           *bool               `json:"pfcCfmed"`
	ToolingCFMed       *bool               `json:"toolingCfmed"`
	FSTComment         *string             `json:"fstComment"`

	// Manufacturing Stage (PROD MATERIAL UPDATE)
	ProdMaterialUpdateDate *types.FlexibleTime `json:"prodMaterialUpdateDate"`
	PhysicalTestPass       *bool               `json:"physicalTestPass"`
	VisualCheckPass        *bool               `json:"visualCheckPass"`
	ProdMaterialComment    *string             `json:"prodMaterialComment"`

	// Manufacturing Stage (MPPA UPDATE - Process Assessment)
	ProcessAssessmentDate *types.FlexibleTime `json:"processAssessmentDate"`
	MPPACheckProcess      *bool               `json:"mppaCheckProcess"`
	MPPAPassProcess       *bool               `json:"mppaPassProcess"`
	ProcessComment        *string             `json:"processComment"`

	// Manufacturing Stage (MPPA UPDATE - Product Assessment)
	ProductAssessmentDate *types.FlexibleTime `json:"productAssessmentDate"`
	MPPACheckProduct      *bool               `json:"mppaCheckProduct"`
	MPPAPassProduct       *bool               `json:"mppaPassProduct"`
	ProductComment        *string             `json:"productComment"`

	// User tracking
	UserID *string `json:"userId"`
}

// Lookup table requests
type SeasonRequest struct {
	SeasonCode string `json:"seasonCode" binding:"required"`
	SeasonName string `json:"seasonName" binding:"required"`
	IsActive   *bool  `json:"isActive"`
}

type FactoryRequest struct {
	FactoryCode string  `json:"factoryCode" binding:"required"`
	FactoryName string  `json:"factoryName" binding:"required"`
	Location    *string `json:"location"`
	IsActive    *bool   `json:"isActive"`
}

type KeyModelTypeRequest struct {
	TypeCode    string  `json:"typeCode" binding:"required"`
	TypeName    string  `json:"typeName" binding:"required"`
	Description *string `json:"description"`
	IsActive    *bool   `json:"isActive"`
}

// Filter requests
type KeyModelRecordFilterRequest struct {
	ColorwayID   *string `form:"colorwayId" json:"colorwayId"`
	MaterialID   *string `form:"materialId" json:"materialId"`
	KeyModelType *string `form:"keyModelType" json:"keyModelType"`
	ProductName  *string `form:"productName" json:"productName"`
	ISD          *string `form:"isd" json:"isd"`
	FTY          *string `form:"fty" json:"fty"`
	Season       *string `form:"season" json:"season"`
	IsActive     *bool   `form:"isActive" json:"isActive"`
	PageNumber   int     `form:"pageNumber" json:"pageNumber"`
	PageSize     int     `form:"pageSize" json:"pageSize"`
}

// KfxxzlFilterRequest dùng cho filter và phân trang API kfxxzl-rows
// Các trường đều là optional, phân trang mặc định page=1, pageSize=20
type KfxxzlFilterRequest struct {
	ColorwayId  string `form:"colorwayId"`
	MaterialId  string `form:"materialId"`
	Season      string `form:"season"`
	Factory     string `form:"fty"`
	XXZLArticle string `form:"xxzlArticle"`
	Page        int    `form:"page"`
	PageSize    int    `form:"pageSize"`
}
