package entities

import (
	"time"

	"gorm.io/gorm"
)

// KeyModelRecord represents the unified key model tracking table
type KeyModelRecord struct {
	// Basic Information
	Id             SQLServerUUID `json:"id" gorm:"column:Id;type:uniqueidentifier;primaryKey"`
	ColorwayId     string        `json:"colorwayId" gorm:"column:ColorwayId"`
	MaterialId     string        `json:"materialId" gorm:"column:MaterialId"`
	KeyModelType   string        `json:"keyModelType" gorm:"column:KeyModelType"`
	ProductName    string        `json:"productName" gorm:"column:ProductName"`
	FTY            string        `json:"fty" gorm:"column:FTY"`
	Quantity       *int          `json:"quantity" gorm:"column:Quantity"`
	ISD            string        `json:"isd" gorm:"column:ISD"`
	Season         string        `json:"season" gorm:"column:Season"`
	POReceivedDate *time.Time    `json:"poReceivedDate" gorm:"column:POReceivedDate"`

	// Product Creation Stage (GTMF RFC UPDATE)
	UpdateDate       *time.Time `gorm:"column:UpdateDate;type:datetime2" json:"updateDate"`
	POReceived       bool       `gorm:"column:POReceived;type:bit;default:0" json:"poReceived"`
	PFCValidated     bool       `gorm:"column:PFCValidated;type:bit;default:0" json:"pfcValidated"`
	PTRSSCFMed       bool       `gorm:"column:PTRSSCFMed;type:bit;default:0" json:"ptrssCfmed"`
	DesignKeyFeature *string    `gorm:"column:DesignKeyFeature;type:nvarchar(max)" json:"designKeyFeature"`
	DevLearning      *string    `gorm:"column:DevLearning;type:nvarchar(max)" json:"devLearning"`
	HRMaterialUpdate *string    `gorm:"column:HRMaterialUpdate;type:nvarchar(500)" json:"hrMaterialUpdate"`
	GTMComment       *string    `gorm:"column:GTMComment;type:nvarchar(max)" json:"gtmComment"`

	// Commercial Stage (EST UPDATE)
	ESTDate         *time.Time `gorm:"column:ESTDate;type:datetime2" json:"estDate"`
	FirstESTPass    bool       `gorm:"column:FirstESTPass;type:bit;default:0" json:"firstEstPass"`
	SecondESTPass   bool       `gorm:"column:SecondESTPass;type:bit;default:0" json:"secondEstPass"`
	FittingApproval bool       `gorm:"column:FittingApproval;type:bit;default:0" json:"fittingApproval"`
	PTRSSTested     bool       `gorm:"column:PTRSSTested;type:bit;default:0" json:"ptrssTestedEst"`
	ESTComment      *string    `gorm:"column:ESTComment;type:nvarchar(max)" json:"estComment"`

	// Commercial Stage (FST UPDATE)
	SampleReceivedDate *time.Time `gorm:"column:SampleReceivedDate;type:datetime2" json:"sampleReceivedDate"`
	FSTDate            *time.Time `gorm:"column:FSTDate;type:datetime2" json:"fstDate"`
	FirstFSTPass       bool       `gorm:"column:FirstFSTPass;type:bit;default:0" json:"firstFstPass"`
	SecondFSTPass      bool       `gorm:"column:SecondFSTPass;type:bit;default:0" json:"secondFstPass"`
	PTRSSTestedFST     bool       `gorm:"column:PTRSSTestedFST;type:bit;default:0" json:"ptrssTestedFst"`
	PFCCFMed           bool       `gorm:"column:PFCCFMed;type:bit;default:0" json:"pfcCfmed"`
	ToolingCFMed       bool       `gorm:"column:ToolingCFMed;type:bit;default:0" json:"toolingCfmed"`
	FSTComment         *string    `gorm:"column:FSTComment;type:nvarchar(max)" json:"fstComment"`

	// Manufacturing Stage (PROD MATERIAL UPDATE)
	ProdMaterialUpdateDate *time.Time `gorm:"column:ProdMaterialUpdateDate;type:datetime2" json:"prodMaterialUpdateDate"`
	PhysicalTestPass       bool       `gorm:"column:PhysicalTestPass;type:bit;default:0" json:"physicalTestPass"`
	VisualCheckPass        bool       `gorm:"column:VisualCheckPass;type:bit;default:0" json:"visualCheckPass"`
	ProdMaterialComment    *string    `gorm:"column:ProdMaterialComment;type:nvarchar(max)" json:"prodMaterialComment"`

	// Manufacturing Stage (MPPA UPDATE - Process Assessment)
	ProcessAssessmentDate *time.Time `gorm:"column:ProcessAssessmentDate;type:datetime2" json:"processAssessmentDate"`
	MPPACheckProcess      bool       `gorm:"column:MPPACheckProcess;type:bit;default:0" json:"mppaCheckProcess"`
	MPPAPassProcess       bool       `gorm:"column:MPPAPassProcess;type:bit;default:0" json:"mppaPassProcess"`
	ProcessComment        *string    `gorm:"column:ProcessComment;type:nvarchar(max)" json:"processComment"`

	// Manufacturing Stage (MPPA UPDATE - Product Assessment)
	ProductAssessmentDate *time.Time `gorm:"column:ProductAssessmentDate;type:datetime2" json:"productAssessmentDate"`
	MPPACheckProduct      bool       `gorm:"column:MPPACheckProduct;type:bit;default:0" json:"mppaCheckProduct"`
	MPPAPassProduct       bool       `gorm:"column:MPPAPassProduct;type:bit;default:0" json:"mppaPassProduct"`
	ProductComment        *string    `gorm:"column:ProductComment;type:nvarchar(max)" json:"productComment"`

	// User tracking
	UserID *string `gorm:"column:UserID;type:nvarchar(50)" json:"userId"`
}

// BeforeCreate will set a UUID rather than numeric ID.
// func (kmr *KeyModelRecord) BeforeCreate(tx *gorm.DB) error {
// 	if kmr.Id == uuid.Nil {
// 		kmr.Id = uuid.New()
// 	}
// 	return nil
// }

// BeforeUpdate will update the UpdatedDate
func (kmr *KeyModelRecord) BeforeUpdate(tx *gorm.DB) error {
	return nil
}

// TableName specifies the table name for GORM
func (KeyModelRecord) TableName() string {
	return "KeyModelRecords"
}
