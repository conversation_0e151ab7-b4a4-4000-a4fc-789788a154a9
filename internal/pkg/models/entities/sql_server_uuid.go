package entities

import (
	"database/sql/driver"
	"encoding/json"

	"github.com/google/uuid"
)

type SQLServerUUID uuid.UUID

func (u *SQLServerUUID) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	switch v := value.(type) {
	case []byte:
		if len(v) == 16 {
			// SQL Server stores UUID with different byte order
			// Need to swap bytes to match standard UUID format
			var reordered [16]byte

			// Swap first 4 bytes (DWORD)
			reordered[0] = v[3]
			reordered[1] = v[2]
			reordered[2] = v[1]
			reordered[3] = v[0]

			// Swap next 2 bytes (WORD)
			reordered[4] = v[5]
			reordered[5] = v[4]

			// Swap next 2 bytes (WORD)
			reordered[6] = v[7]
			reordered[7] = v[6]

			// Last 8 bytes stay the same
			copy(reordered[8:], v[8:])

			parsed, err := uuid.FromBytes(reordered[:])
			if err != nil {
				return err
			}
			*u = SQLServerUUID(parsed)
		}
	case string:
		parsed, err := uuid.Parse(v)
		if err != nil {
			return err
		}
		*u = SQLServerUUID(parsed)
	}
	return nil
}

func (u SQLServerUUID) Value() (driver.Value, error) {
	return uuid.UUID(u).String(), nil
}

func (u SQLServerUUID) String() string {
	return uuid.UUID(u).String()
}

func (u SQLServerUUID) MarshalJSON() ([]byte, error) {
	return json.Marshal(u.String())
}

func (u *SQLServerUUID) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}
	parsed, err := uuid.Parse(s)
	if err != nil {
		return err
	}
	*u = SQLServerUUID(parsed)
	return nil
}
