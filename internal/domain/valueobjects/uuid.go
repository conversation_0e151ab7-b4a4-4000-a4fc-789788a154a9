package valueobjects

import (
	"database/sql/driver"
	"fmt"
	"strings"

	"github.com/google/uuid"
)

// UUID represents a domain UUID value object
type UUID struct {
	value uuid.UUID
}

// NewUUID creates a new UUID
func NewUUID() (UUID, error) {
	id, err := uuid.NewRandom()
	if err != nil {
		return UUID{}, fmt.<PERSON><PERSON><PERSON>("failed to generate UUID: %w", err)
	}
	return UUID{value: id}, nil
}

// NewUUIDFromString creates a UUID from string
func NewUUIDFromString(s string) (UUID, error) {
	if s == "" {
		return UUID{}, fmt.Errorf("UUID string cannot be empty")
	}
	
	id, err := uuid.Parse(s)
	if err != nil {
		return UUID{}, fmt.Errorf("invalid UUID format: %w", err)
	}
	return UUID{value: id}, nil
}

// String returns the string representation
func (u UUID) String() string {
	return u.value.String()
}

// Value returns the underlying uuid.UUID
func (u UUID) Value() uuid.UUID {
	return u.value
}

// IsEmpty checks if UUID is empty
func (u UUID) IsEmpty() bool {
	return u.value == uuid.Nil
}

// Equals compares two UUIDs
func (u UUID) Equals(other UUID) bool {
	return u.value == other.value
}

// MarshalJSON implements json.Marshaler
func (u UUID) MarshalJSON() ([]byte, error) {
	return []byte(`"` + u.value.String() + `"`), nil
}

// UnmarshalJSON implements json.Unmarshaler
func (u *UUID) UnmarshalJSON(data []byte) error {
	str := strings.Trim(string(data), `"`)
	if str == "null" || str == "" {
		u.value = uuid.Nil
		return nil
	}
	
	id, err := uuid.Parse(str)
	if err != nil {
		return fmt.Errorf("invalid UUID format: %w", err)
	}
	u.value = id
	return nil
}

// Scan implements sql.Scanner for database operations
func (u *UUID) Scan(value interface{}) error {
	if value == nil {
		u.value = uuid.Nil
		return nil
	}
	
	switch v := value.(type) {
	case string:
		id, err := uuid.Parse(v)
		if err != nil {
			return fmt.Errorf("invalid UUID format: %w", err)
		}
		u.value = id
	case []byte:
		id, err := uuid.Parse(string(v))
		if err != nil {
			return fmt.Errorf("invalid UUID format: %w", err)
		}
		u.value = id
	case uuid.UUID:
		u.value = v
	default:
		return fmt.Errorf("cannot scan %T into UUID", value)
	}
	
	return nil
}

// Value implements driver.Valuer for database operations
func (u UUID) Value() (driver.Value, error) {
	if u.value == uuid.Nil {
		return nil, nil
	}
	return u.value.String(), nil
}

// NilUUID returns an empty UUID
func NilUUID() UUID {
	return UUID{value: uuid.Nil}
}
