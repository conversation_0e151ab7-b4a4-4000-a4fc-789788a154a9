package entities

import (
	"fmt"
	"time"

	"web-api/internal/domain/valueobjects"
	"web-api/internal/shared/errors"
)

// StageType represents the different stages in key model tracking
type StageType string

const (
	StageProductCreation StageType = "PRODUCT_CREATION"
	StageCommercialEST   StageType = "COMMERCIAL_EST"
	StageCommercialFST   StageType = "COMMERCIAL_FST"
	StageManufacturing   StageType = "MANUFACTURING"
	StageCompleted       StageType = "COMPLETED"
)

// StageStatus represents the status of a stage
type StageStatus string

const (
	StatusNotStarted StageStatus = "NOT_STARTED"
	StatusInProgress StageStatus = "IN_PROGRESS"
	StatusCompleted  StageStatus = "COMPLETED"
	StatusFailed     StageStatus = "FAILED"
)

// KeyModelRecord represents the core domain entity for key model tracking
type KeyModelRecord struct {
	// Identity
	id valueobjects.UUID

	// Basic Information
	colorwayId   string
	materialId   string
	keyModelType string
	productName  string
	fty          string
	quantity     *int
	isd          string
	season       string

	// Tracking Information
	currentStage StageType
	stages       map[StageType]*Stage
	
	// Audit Information
	createdAt time.Time
	updatedAt time.Time
	createdBy string
	updatedBy string
	isActive  bool

	// Business Rules
	poReceivedDate *time.Time
}

// Stage represents a stage in the key model tracking process
type Stage struct {
	Type        StageType
	Status      StageStatus
	StartedAt   *time.Time
	CompletedAt *time.Time
	Data        map[string]interface{}
	Comments    string
	UpdatedBy   string
}

// NewKeyModelRecord creates a new key model record with business validation
func NewKeyModelRecord(
	colorwayId, materialId, keyModelType, productName, fty, isd, season, createdBy string,
	quantity *int,
	poReceivedDate *time.Time,
) (*KeyModelRecord, error) {
	// Business validation
	if err := validateBasicInfo(colorwayId, materialId, keyModelType, productName, fty, isd, season); err != nil {
		return nil, err
	}

	id, err := valueobjects.NewUUID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate UUID: %w", err)
	}

	now := time.Now()
	
	record := &KeyModelRecord{
		id:             id,
		colorwayId:     colorwayId,
		materialId:     materialId,
		keyModelType:   keyModelType,
		productName:    productName,
		fty:            fty,
		quantity:       quantity,
		isd:            isd,
		season:         season,
		currentStage:   StageProductCreation,
		stages:         make(map[StageType]*Stage),
		createdAt:      now,
		updatedAt:      now,
		createdBy:      createdBy,
		updatedBy:      createdBy,
		isActive:       true,
		poReceivedDate: poReceivedDate,
	}

	// Initialize first stage
	record.initializeStage(StageProductCreation)

	return record, nil
}

// GetID returns the record ID
func (r *KeyModelRecord) GetID() valueobjects.UUID {
	return r.id
}

// GetColorwayId returns the colorway ID
func (r *KeyModelRecord) GetColorwayId() string {
	return r.colorwayId
}

// GetMaterialId returns the material ID
func (r *KeyModelRecord) GetMaterialId() string {
	return r.materialId
}

// GetCurrentStage returns the current stage
func (r *KeyModelRecord) GetCurrentStage() StageType {
	return r.currentStage
}

// GetStage returns a specific stage
func (r *KeyModelRecord) GetStage(stageType StageType) *Stage {
	return r.stages[stageType]
}

// GetAllStages returns all stages
func (r *KeyModelRecord) GetAllStages() map[StageType]*Stage {
	return r.stages
}

// AdvanceToNextStage advances the record to the next stage
func (r *KeyModelRecord) AdvanceToNextStage(updatedBy string) error {
	currentStage := r.stages[r.currentStage]
	if currentStage == nil {
		return errors.NewDomainError("current stage not found")
	}

	if currentStage.Status != StatusCompleted {
		return errors.NewDomainError("current stage must be completed before advancing")
	}

	nextStage := r.getNextStage()
	if nextStage == "" {
		return errors.NewDomainError("no next stage available")
	}

	r.currentStage = nextStage
	r.initializeStage(nextStage)
	r.updatedAt = time.Now()
	r.updatedBy = updatedBy

	return nil
}

// UpdateStageData updates data for a specific stage
func (r *KeyModelRecord) UpdateStageData(stageType StageType, data map[string]interface{}, updatedBy string) error {
	stage := r.stages[stageType]
	if stage == nil {
		return errors.NewDomainError("stage not found")
	}

	// Business rule: can only update current or previous stages
	if !r.canUpdateStage(stageType) {
		return errors.NewDomainError("cannot update future stages")
	}

	// Merge data
	if stage.Data == nil {
		stage.Data = make(map[string]interface{})
	}
	for k, v := range data {
		stage.Data[k] = v
	}

	stage.UpdatedBy = updatedBy
	r.updatedAt = time.Now()
	r.updatedBy = updatedBy

	return nil
}

// CompleteStage marks a stage as completed
func (r *KeyModelRecord) CompleteStage(stageType StageType, updatedBy string) error {
	stage := r.stages[stageType]
	if stage == nil {
		return errors.NewDomainError("stage not found")
	}

	if stage.Status == StatusCompleted {
		return errors.NewDomainError("stage already completed")
	}

	// Business validation for stage completion
	if err := r.validateStageCompletion(stageType); err != nil {
		return err
	}

	now := time.Now()
	stage.Status = StatusCompleted
	stage.CompletedAt = &now
	stage.UpdatedBy = updatedBy
	r.updatedAt = now
	r.updatedBy = updatedBy

	return nil
}

// UpdateBasicInfo updates basic information with validation
func (r *KeyModelRecord) UpdateBasicInfo(
	colorwayId, materialId, keyModelType, productName, fty, isd, season, updatedBy string,
	quantity *int,
) error {
	if err := validateBasicInfo(colorwayId, materialId, keyModelType, productName, fty, isd, season); err != nil {
		return err
	}

	r.colorwayId = colorwayId
	r.materialId = materialId
	r.keyModelType = keyModelType
	r.productName = productName
	r.fty = fty
	r.quantity = quantity
	r.isd = isd
	r.season = season
	r.updatedAt = time.Now()
	r.updatedBy = updatedBy

	return nil
}

// Deactivate marks the record as inactive
func (r *KeyModelRecord) Deactivate(updatedBy string) {
	r.isActive = false
	r.updatedAt = time.Now()
	r.updatedBy = updatedBy
}

// Activate marks the record as active
func (r *KeyModelRecord) Activate(updatedBy string) {
	r.isActive = true
	r.updatedAt = time.Now()
	r.updatedBy = updatedBy
}

// IsActive returns whether the record is active
func (r *KeyModelRecord) IsActive() bool {
	return r.isActive
}

// Private helper methods

func (r *KeyModelRecord) initializeStage(stageType StageType) {
	now := time.Now()
	r.stages[stageType] = &Stage{
		Type:      stageType,
		Status:    StatusInProgress,
		StartedAt: &now,
		Data:      make(map[string]interface{}),
	}
}

func (r *KeyModelRecord) getNextStage() StageType {
	switch r.currentStage {
	case StageProductCreation:
		return StageCommercialEST
	case StageCommercialEST:
		return StageCommercialFST
	case StageCommercialFST:
		return StageManufacturing
	case StageManufacturing:
		return StageCompleted
	default:
		return ""
	}
}

func (r *KeyModelRecord) canUpdateStage(stageType StageType) bool {
	stageOrder := map[StageType]int{
		StageProductCreation: 1,
		StageCommercialEST:   2,
		StageCommercialFST:   3,
		StageManufacturing:   4,
		StageCompleted:       5,
	}

	currentOrder := stageOrder[r.currentStage]
	targetOrder := stageOrder[stageType]

	return targetOrder <= currentOrder
}

func (r *KeyModelRecord) validateStageCompletion(stageType StageType) error {
	stage := r.stages[stageType]
	
	// Stage-specific validation rules
	switch stageType {
	case StageProductCreation:
		return r.validateProductCreationCompletion(stage)
	case StageCommercialEST:
		return r.validateCommercialESTCompletion(stage)
	case StageCommercialFST:
		return r.validateCommercialFSTCompletion(stage)
	case StageManufacturing:
		return r.validateManufacturingCompletion(stage)
	}

	return nil
}

func (r *KeyModelRecord) validateProductCreationCompletion(stage *Stage) error {
	// Business rules for product creation completion
	if r.poReceivedDate == nil {
		return errors.NewDomainError("PO received date is required for product creation completion")
	}
	return nil
}

func (r *KeyModelRecord) validateCommercialESTCompletion(stage *Stage) error {
	// Business rules for EST completion
	return nil
}

func (r *KeyModelRecord) validateCommercialFSTCompletion(stage *Stage) error {
	// Business rules for FST completion
	return nil
}

func (r *KeyModelRecord) validateManufacturingCompletion(stage *Stage) error {
	// Business rules for manufacturing completion
	return nil
}

// validateBasicInfo validates basic information fields
func validateBasicInfo(colorwayId, materialId, keyModelType, productName, fty, isd, season string) error {
	if colorwayId == "" {
		return errors.NewValidationError("colorway ID is required")
	}
	if materialId == "" {
		return errors.NewValidationError("material ID is required")
	}
	if keyModelType == "" {
		return errors.NewValidationError("key model type is required")
	}
	if productName == "" {
		return errors.NewValidationError("product name is required")
	}
	if fty == "" {
		return errors.NewValidationError("factory is required")
	}
	if isd == "" {
		return errors.NewValidationError("ISD is required")
	}
	if season == "" {
		return errors.NewValidationError("season is required")
	}
	return nil
}
