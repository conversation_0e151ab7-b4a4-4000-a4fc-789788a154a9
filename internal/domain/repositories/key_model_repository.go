package repositories

import (
	"context"

	"web-api/internal/domain/entities"
	"web-api/internal/domain/valueobjects"
)

// FilterCriteria represents search and filter criteria
type FilterCriteria struct {
	ColorwayId   *string
	MaterialId   *string
	KeyModelType *string
	ProductName  *string
	FTY          *string
	ISD          *string
	Season       *string
	IsActive     *bool
	
	// Stage-specific filters
	CurrentStage *entities.StageType
	StageStatus  *entities.StageStatus
	
	// Date filters
	CreatedAfter  *string
	CreatedBefore *string
	UpdatedAfter  *string
	UpdatedBefore *string
	
	// Pagination
	Offset int
	Limit  int
	
	// Sorting
	SortBy    string
	SortOrder string // ASC or DESC
}

// PaginationResult represents paginated results
type PaginationResult struct {
	Records    []*entities.KeyModelRecord
	TotalCount int64
	Page       int
	PageSize   int
	TotalPages int
}

// KeyModelRepository defines the interface for key model record persistence
type KeyModelRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, record *entities.KeyModelRecord) error
	GetByID(ctx context.Context, id valueobjects.UUID) (*entities.KeyModelRecord, error)
	Update(ctx context.Context, record *entities.KeyModelRecord) error
	Delete(ctx context.Context, id valueobjects.UUID) error
	
	// Query operations
	FindByFilters(ctx context.Context, criteria FilterCriteria) ([]*entities.KeyModelRecord, error)
	FindByFiltersWithPagination(ctx context.Context, criteria FilterCriteria) (*PaginationResult, error)
	
	// Specific queries
	FindByColorwayAndMaterial(ctx context.Context, colorwayId, materialId string) ([]*entities.KeyModelRecord, error)
	FindByMaterialId(ctx context.Context, materialId string, filters map[string]string) ([]*entities.KeyModelRecord, error)
	FindByMultipleParams(ctx context.Context, colorwayId, materialId, fty, season string) ([]*entities.KeyModelRecord, error)
	
	// Distinct value queries
	GetDistinctValues(ctx context.Context, field string) ([]string, error)
	GetDistinctColorways(ctx context.Context) ([]string, error)
	GetDistinctMaterials(ctx context.Context) ([]string, error)
	GetDistinctSeasons(ctx context.Context) ([]string, error)
	GetDistinctFactories(ctx context.Context) ([]string, error)
	
	// Business queries
	FindByStage(ctx context.Context, stage entities.StageType) ([]*entities.KeyModelRecord, error)
	FindOverdueRecords(ctx context.Context, days int) ([]*entities.KeyModelRecord, error)
	FindRecordsRequiringAttention(ctx context.Context) ([]*entities.KeyModelRecord, error)
	
	// Bulk operations
	BulkUpdate(ctx context.Context, ids []valueobjects.UUID, updates map[string]interface{}) error
	BulkDelete(ctx context.Context, ids []valueobjects.UUID) error
	
	// Statistics
	GetStageStatistics(ctx context.Context) (map[entities.StageType]int64, error)
	GetFactoryStatistics(ctx context.Context) (map[string]int64, error)
	GetSeasonStatistics(ctx context.Context) (map[string]int64, error)
	
	// Existence checks
	ExistsByColorwayAndMaterial(ctx context.Context, colorwayId, materialId string) (bool, error)
	ExistsByID(ctx context.Context, id valueobjects.UUID) (bool, error)
	
	// Count operations
	Count(ctx context.Context, criteria FilterCriteria) (int64, error)
	CountByStage(ctx context.Context, stage entities.StageType) (int64, error)
	CountByFactory(ctx context.Context, factory string) (int64, error)
	
	// Transaction support
	WithTransaction(ctx context.Context, fn func(repo KeyModelRepository) error) error
}

// StageUpdateData represents data for updating a specific stage
type StageUpdateData struct {
	StageType entities.StageType
	Data      map[string]interface{}
	Comments  string
	UpdatedBy string
}

// BulkCreateRequest represents a request for bulk creation
type BulkCreateRequest struct {
	Records   []*entities.KeyModelRecord
	BatchSize int
}

// SearchOptions represents advanced search options
type SearchOptions struct {
	IncludeInactive bool
	IncludeStages   bool
	IncludeAudit    bool
}

// KeyModelRepositoryExtended extends the base repository with advanced features
type KeyModelRepositoryExtended interface {
	KeyModelRepository
	
	// Advanced search
	Search(ctx context.Context, query string, options SearchOptions) ([]*entities.KeyModelRecord, error)
	
	// Bulk operations
	BulkCreate(ctx context.Context, request BulkCreateRequest) error
	BulkUpdateStages(ctx context.Context, updates []StageUpdateData) error
	
	// Export/Import
	ExportToCSV(ctx context.Context, criteria FilterCriteria) ([]byte, error)
	ImportFromCSV(ctx context.Context, data []byte) error
	
	// Audit trail
	GetAuditTrail(ctx context.Context, id valueobjects.UUID) ([]AuditEntry, error)
	
	// Performance
	GetSlowQueries(ctx context.Context) ([]SlowQuery, error)
	OptimizeIndexes(ctx context.Context) error
}

// AuditEntry represents an audit trail entry
type AuditEntry struct {
	ID        valueobjects.UUID
	RecordID  valueobjects.UUID
	Action    string
	Changes   map[string]interface{}
	Timestamp string
	UserID    string
}

// SlowQuery represents a slow query for performance monitoring
type SlowQuery struct {
	Query     string
	Duration  float64
	Timestamp string
	Count     int64
}

// RepositoryOptions represents options for repository configuration
type RepositoryOptions struct {
	EnableAudit     bool
	EnableCache     bool
	CacheTTL        int
	MaxConnections  int
	QueryTimeout    int
	EnableMetrics   bool
}

// KeyModelRepositoryFactory creates repository instances
type KeyModelRepositoryFactory interface {
	CreateRepository(options RepositoryOptions) KeyModelRepository
	CreateExtendedRepository(options RepositoryOptions) KeyModelRepositoryExtended
}

// MockKeyModelRepository provides a mock implementation for testing
type MockKeyModelRepository struct {
	records map[string]*entities.KeyModelRecord
}

// NewMockKeyModelRepository creates a new mock repository
func NewMockKeyModelRepository() *MockKeyModelRepository {
	return &MockKeyModelRepository{
		records: make(map[string]*entities.KeyModelRecord),
	}
}

// Create implements KeyModelRepository.Create for testing
func (m *MockKeyModelRepository) Create(ctx context.Context, record *entities.KeyModelRecord) error {
	m.records[record.GetID().String()] = record
	return nil
}

// GetByID implements KeyModelRepository.GetByID for testing
func (m *MockKeyModelRepository) GetByID(ctx context.Context, id valueobjects.UUID) (*entities.KeyModelRecord, error) {
	record, exists := m.records[id.String()]
	if !exists {
		return nil, &entities.KeyModelRecordNotFoundError{ID: id.String()}
	}
	return record, nil
}

// Update implements KeyModelRepository.Update for testing
func (m *MockKeyModelRepository) Update(ctx context.Context, record *entities.KeyModelRecord) error {
	m.records[record.GetID().String()] = record
	return nil
}

// Delete implements KeyModelRepository.Delete for testing
func (m *MockKeyModelRepository) Delete(ctx context.Context, id valueobjects.UUID) error {
	delete(m.records, id.String())
	return nil
}

// Additional mock methods would be implemented here...
// For brevity, showing just the basic CRUD operations

// KeyModelRecordNotFoundError represents a not found error
type KeyModelRecordNotFoundError struct {
	ID string
}

func (e *KeyModelRecordNotFoundError) Error() string {
	return "key model record with ID " + e.ID + " not found"
}
