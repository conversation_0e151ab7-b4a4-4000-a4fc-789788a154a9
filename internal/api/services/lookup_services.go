package services

import (
	"errors"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/entities"
	"web-api/internal/pkg/models/request"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SeasonService handles season operations
type SeasonService struct {
	BaseService
}

var Season = &SeasonService{}

// Create creates a new season
func (s *SeasonService) Create(req *request.SeasonRequest) (*entities.Season, error) {
	season := &entities.Season{
		SeasonCode: req.SeasonCode,
		SeasonName: req.SeasonName,
		IsActive:   true,
	}

	if req.IsActive != nil {
		season.IsActive = *req.IsActive
	}

	if err := s.BaseService.Create(season); err != nil {
		return nil, err
	}

	return season, nil
}

// GetAll retrieves all active seasons
func (s *SeasonService) GetAll() ([]entities.Season, error) {
	var seasons []entities.Season
	err := database.GetDB().Where("is_active = ?", true).Order("season_name").Find(&seasons).Error
	return seasons, err
}

// GetByID retrieves a season by ID
func (s *SeasonService) GetByID(id uuid.UUID) (*entities.Season, error) {
	var season entities.Season
	err := database.GetDB().Where("Id = ?", id).First(&season).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("season not found")
		}
		return nil, err
	}
	return &season, nil
}

// Update updates a season
func (s *SeasonService) Update(id uuid.UUID, req *request.SeasonRequest) (*entities.Season, error) {
	var season entities.Season

	if err := database.GetDB().Where("Id = ?", id).First(&season).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("season not found")
		}
		return nil, err
	}

	updates := map[string]interface{}{
		"season_code": req.SeasonCode,
		"season_name": req.SeasonName,
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	if err := database.GetDB().Model(&season).Updates(updates).Error; err != nil {
		return nil, err
	}

	return &season, nil
}

// Delete soft deletes a season
func (s *SeasonService) Delete(id uuid.UUID) error {
	var season entities.Season

	if err := database.GetDB().Where("Id = ?", id).First(&season).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("season not found")
		}
		return err
	}

	return database.GetDB().Model(&season).Update("IsActive", false).Error
}

// FactoryService handles factory operations
type FactoryService struct {
	BaseService
}

var Factory = &FactoryService{}

// Create creates a new factory
func (s *FactoryService) Create(req *request.FactoryRequest) (*entities.Factory, error) {
	factory := &entities.Factory{
		FactoryCode: req.FactoryCode,
		FactoryName: req.FactoryName,
		Location:    req.Location,
		IsActive:    true,
	}

	if req.IsActive != nil {
		factory.IsActive = *req.IsActive
	}

	if err := s.BaseService.Create(factory); err != nil {
		return nil, err
	}

	return factory, nil
}

// GetAll retrieves all active factories
func (s *FactoryService) GetAll() ([]entities.Factory, error) {
	var factories []entities.Factory
	err := database.GetDB().Where("is_active = ?", true).Order("factory_name").Find(&factories).Error
	return factories, err
}

// GetByID retrieves a factory by ID
func (s *FactoryService) GetByID(id int) (*entities.Factory, error) {
	var factory entities.Factory
	err := database.GetDB().Where("id = ? AND is_active = ?", id, true).First(&factory).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("factory not found")
		}
		return nil, err
	}
	return &factory, nil
}

// Update updates a factory
func (s *FactoryService) Update(id int, req *request.FactoryRequest) (*entities.Factory, error) {
	var factory entities.Factory

	if err := database.GetDB().Where("id = ? AND is_active = ?", id, true).First(&factory).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("factory not found")
		}
		return nil, err
	}

	updates := map[string]interface{}{
		"factory_code": req.FactoryCode,
		"factory_name": req.FactoryName,
		"location":     req.Location,
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	if err := database.GetDB().Model(&factory).Updates(updates).Error; err != nil {
		return nil, err
	}

	return &factory, nil
}

// Delete soft deletes a factory
func (s *FactoryService) Delete(id int) error {
	var factory entities.Factory

	if err := database.GetDB().Where("id = ? AND is_active = ?", id, true).First(&factory).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("factory not found")
		}
		return err
	}

	return database.GetDB().Model(&factory).Update("is_active", false).Error
}

// KeyModelTypeService handles key model type operations
type KeyModelTypeService struct {
	BaseService
}

var KeyModelType = &KeyModelTypeService{}

// Create creates a new key model type
func (s *KeyModelTypeService) Create(req *request.KeyModelTypeRequest) (*entities.KeyModelType, error) {
	keyModelType := &entities.KeyModelType{
		TypeCode:    req.TypeCode,
		TypeName:    req.TypeName,
		Description: req.Description,
		IsActive:    true,
	}

	if req.IsActive != nil {
		keyModelType.IsActive = *req.IsActive
	}

	if err := s.BaseService.Create(keyModelType); err != nil {
		return nil, err
	}

	return keyModelType, nil
}

// GetAll retrieves all active key model types
func (s *KeyModelTypeService) GetAll() ([]entities.KeyModelType, error) {
	var keyModelTypes []entities.KeyModelType
	err := database.GetDB().Where("is_active = ?", true).Order("type_name").Find(&keyModelTypes).Error
	return keyModelTypes, err
}

// GetByID retrieves a key model type by ID
func (s *KeyModelTypeService) GetByID(id int) (*entities.KeyModelType, error) {
	var keyModelType entities.KeyModelType
	err := database.GetDB().Where("id = ? AND is_active = ?", id, true).First(&keyModelType).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("key model type not found")
		}
		return nil, err
	}
	return &keyModelType, nil
}

// Update updates a key model type
func (s *KeyModelTypeService) Update(id int, req *request.KeyModelTypeRequest) (*entities.KeyModelType, error) {
	var keyModelType entities.KeyModelType

	if err := database.GetDB().Where("id = ? AND is_active = ?", id, true).First(&keyModelType).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("key model type not found")
		}
		return nil, err
	}

	updates := map[string]interface{}{
		"type_code":   req.TypeCode,
		"type_name":   req.TypeName,
		"description": req.Description,
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	if err := database.GetDB().Model(&keyModelType).Updates(updates).Error; err != nil {
		return nil, err
	}

	return &keyModelType, nil
}

// Delete soft deletes a key model type
func (s *KeyModelTypeService) Delete(id int) error {
	var keyModelType entities.KeyModelType

	if err := database.GetDB().Where("id = ? AND is_active = ?", id, true).First(&keyModelType).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("key model type not found")
		}
		return err
	}

	return database.GetDB().Model(&keyModelType).Update("is_active", false).Error
}
