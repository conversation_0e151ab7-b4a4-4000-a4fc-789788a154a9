package services

import (
	"database/sql"
	"fmt"
	"strings"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/request"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"
)

type CommonService struct {
	*BaseService
}

var Common = &CommonService{}

func (s *CommonService) Login(params *types.User) (any, error) {
	var user types.User

	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	query := `
	SELECT 
		CAST(USERID AS NVARCHAR(36)) AS UserID,
		USERNAME AS UserName,
		PWD AS Password,
		Role
	FROM [ERP_23_9].[LIY_ERP].[dbo].[BUSERS]
	WHERE USERID = ?
`

	err = db.Raw(query, params.UserID).Scan(&user).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}

	// Không tìm thấy người dùng
	if user.UserID == "" {
		return "Account does not exist or has been locked", nil
	}

	// So sánh mật khẩu đơn giản (nếu chưa hash)
	if user.Password != params.Password {
		return "Password is incorrect", nil
	}

	// Gán token (giả lập token)
	user.Token = fmt.Sprintf("token_%s", user.UserID)
	user.Password = "" // clear password khi trả về

	return user, nil
}

// GetDistinctColorways retrieves distinct colorway IDs from kfxxzl table
func (s *CommonService) GetDistinctColorways() ([]string, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	var colorways []string
	query := `SELECT DISTINCT DEVCODE AS ColorwayId FROM [ERP_23_9].[LIY_ERP].[dbo].[kfxxzl] WHERE DEVCODE IS NOT NULL AND kfxxzl.kfcq IN ('JNG', 'YQA')`

	err = db.Raw(query).Scan(&colorways).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}

	return colorways, nil
}

// GetDistinctMaterials retrieves distinct material IDs from kfxxzl table
func (s *CommonService) GetDistinctMaterials() ([]string, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	var materials []string
	query := `SELECT DISTINCT ARTICLE AS MaterialId FROM [ERP_23_9].[LIY_ERP].[dbo].[kfxxzl] WHERE ARTICLE IS NOT NULL  AND kfxxzl.kfcq IN ('JNG', 'YQA')`

	err = db.Raw(query).Scan(&materials).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}

	return materials, nil
}

// GetDistinctSeasons retrieves distinct seasons from kfxxzl table
func (s *CommonService) GetDistinctSeasons() ([]string, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	var seasons []string
	query := `SELECT DISTINCT jijie as Season FROM [ERP_23_9].[LIY_ERP].[dbo].[kfxxzl] WHERE jijie IS NOT NULL  AND kfxxzl.kfcq IN ('JNG', 'YQA')`

	err = db.Raw(query).Scan(&seasons).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}

	return seasons, nil
}

// GetDistinctFactories retrieves distinct factories from kfxxzl table
func (s *CommonService) GetDistinctFactories() ([]string, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	var factories []string
	query := `SELECT DISTINCT kfcq as factory FROM [ERP_23_9].[LIY_ERP].[dbo].[kfxxzl] WHERE kfcq IS NOT NULL   AND kfxxzl.kfcq IN ('JNG', 'YQA')`

	err = db.Raw(query).Scan(&factories).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}

	return factories, nil
}

// Alternative methods that can handle NULL values more robustly
// These methods use sql.NullString to properly handle NULL values

// GetDistinctColorwaysWithNull retrieves distinct colorway IDs including NULL handling
func (s *CommonService) GetDistinctColorwaysWithNull() ([]string, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	type ColorwayResult struct {
		ColorwayId sql.NullString `json:"colorwayId"`
	}

	var results []ColorwayResult
	query := `SELECT DISTINCT DEVCODE AS ColorwayId FROM [ERP_23_9].[LIY_ERP].[dbo].[kfxxzl]`

	err = db.Raw(query).Scan(&results).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}

	var colorways []string
	for _, result := range results {
		if result.ColorwayId.Valid {
			colorways = append(colorways, result.ColorwayId.String)
		}
	}

	return colorways, nil
}

// GetDistinctMaterialsWithNull retrieves distinct material IDs including NULL handling
func (s *CommonService) GetDistinctMaterialsWithNull() ([]string, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	type MaterialResult struct {
		MaterialId sql.NullString `json:"materialId"`
	}

	var results []MaterialResult
	query := `SELECT DISTINCT ARTICLE AS MaterialId FROM [ERP_23_9].[LIY_ERP].[dbo].[kfxxzl]`

	err = db.Raw(query).Scan(&results).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}

	var materials []string
	for _, result := range results {
		if result.MaterialId.Valid {
			materials = append(materials, result.MaterialId.String)
		}
	}

	return materials, nil
}

// GetDistinctSeasonsWithNull retrieves distinct seasons including NULL handling
func (s *CommonService) GetDistinctSeasonsWithNull() ([]string, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	type SeasonResult struct {
		Season sql.NullString `json:"season"`
	}

	var results []SeasonResult
	query := `SELECT DISTINCT jijie as Season FROM [ERP_23_9].[LIY_ERP].[dbo].[kfxxzl]`

	err = db.Raw(query).Scan(&results).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}

	var seasons []string
	for _, result := range results {
		if result.Season.Valid {
			seasons = append(seasons, result.Season.String)
		}
	}

	return seasons, nil
}

// GetDistinctFactoriesWithNull retrieves distinct factories including NULL handling
func (s *CommonService) GetDistinctFactoriesWithNull() ([]string, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	type FactoryResult struct {
		Factory sql.NullString `json:"factory"`
	}

	var results []FactoryResult
	query := `SELECT DISTINCT kfcq as factory FROM [ERP_23_9].[LIY_ERP].[dbo].[kfxxzl]`

	err = db.Raw(query).Scan(&results).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}

	var factories []string
	for _, result := range results {
		if result.Factory.Valid {
			factories = append(factories, result.Factory.String)
		}
	}

	return factories, nil
}

// GetKfxxzlRows retrieves all rows from kfxxzl with ColorwayId, MaterialId, Season, FTY
func (s *CommonService) GetKfxxzlRows() ([]response.KfxxzlRow, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	var rows []response.KfxxzlRow
	query := `SELECT DEVCODE as ColorwayId, ARTICLE as MaterialId, jijie as Season, kfcq as FTY FROM [ERP_23_9].[LIY_ERP].[dbo].[kfxxzl]`
	err = db.Raw(query).Scan(&rows).Error
	if err != nil {
		return nil, err
	}
	return rows, nil
}

// GetKfxxzlRowsFiltered trả về các dòng kfxxzl, filter và phân trang
func (s *CommonService) GetKfxxzlRowsFiltered(req *request.KfxxzlFilterRequest) ([]response.KfxxzlRow, int64, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, 0, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	var rows []response.KfxxzlRow
	var total int64

	// Build dynamic WHERE conditions
	whereClauses := []string{"kfxxzl.DEVCODE IS NOT NULL AND kfxxzl.DEVCODE <> ''"}
	args := []interface{}{}

	// Chỉ lấy 2 factory JNG và YQA
	whereClauses = append(whereClauses, "kfxxzl.kfcq IN ('JNG', 'YQA')")

	if req.ColorwayId != "" {
		whereClauses = append(whereClauses, "kfxxzl.DEVCODE LIKE ?")
		// args = append(args, req.ColorwayId)
		args = append(args, "%"+req.ColorwayId+"%")

	}
	if req.MaterialId != "" {
		whereClauses = append(whereClauses, "XXZL.ARTICLE LIKE ?")
		args = append(args, "%"+req.MaterialId+"%")
	}
	if req.Season != "" {
		whereClauses = append(whereClauses, "kfxxzl.jijie = ?")
		args = append(args, req.Season)
	}
	if req.Factory != "" {
		whereClauses = append(whereClauses, "kfxxzl.kfcq = ?")
		args = append(args, req.Factory)
	}
	// if req.XXZLArticle != "" {
	// 	// Nếu muốn vẫn giữ LEFT JOIN, đưa điều kiện vào ON hoặc để đây thì sẽ giống INNER JOIN
	// 	whereClauses = append(whereClauses, "XXZL.ARTICLE LIKE ?")
	// 	args = append(args, "%"+req.XXZLArticle+"%")
	// }

	whereSQL := ""
	if len(whereClauses) > 0 {
		whereSQL = " WHERE " + strings.Join(whereClauses, " AND ")
	}

	// --- Query 1: Đếm tổng số dòng DISTINCT ---
	countSQL := `
        SELECT COUNT(*) 
        FROM (
            SELECT DISTINCT kfxxzl.DEVCODE, kfxxzl.ARTICLE, kfxxzl.jijie, kfxxzl.kfcq
            FROM [ERP_23_9].[LIY_ERP].[dbo].[kfxxzl] AS kfxxzl
            LEFT JOIN [ERP_23_9].[LIY_ERP].[dbo].[XXZL] AS XXZL
                ON kfxxzl.XieXing = XXZL.XieXing AND kfxxzl.SheHao = XXZL.SheHao 
            ` + whereSQL + `
        ) AS distinct_rows
    `

	if err := db.Raw(countSQL, args...).Scan(&total).Error; err != nil {
		return nil, 0, err
	}

	// --- Query 2: Lấy dữ liệu phân trang ---
	page := req.Page
	pageSize := req.PageSize
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}
	offset := (page - 1) * pageSize

	dataSQL := `
        SELECT DISTINCT 
            kfxxzl.DEVCODE AS ColorwayId, 
            kfxxzl.ARTICLE AS MaterialId, 
            kfxxzl.jijie AS season, 
            kfxxzl.kfcq AS factory
        FROM [ERP_23_9].[LIY_ERP].[dbo].[kfxxzl] AS kfxxzl
        LEFT JOIN [ERP_23_9].[LIY_ERP].[dbo].[XXZL] AS XXZL
            ON kfxxzl.XieXing = XXZL.XieXing AND kfxxzl.SheHao = XXZL.SheHao
        ` + whereSQL + `
        ORDER BY kfxxzl.DEVCODE, kfxxzl.ARTICLE, kfxxzl.jijie, kfxxzl.kfcq
        OFFSET ? ROWS FETCH NEXT ? ROWS ONLY
    `

	// Thêm offset, limit vào args cho câu SELECT
	argsWithLimit := append([]interface{}{}, args...) // copy args gốc
	argsWithLimit = append(argsWithLimit, offset, pageSize)

	if err := db.Raw(dataSQL, argsWithLimit...).Scan(&rows).Error; err != nil {
		return nil, 0, err
	}

	return rows, total, nil
}
