# 🏗️ New Architecture Design - Key Model Tracking API

## 📋 Overview
Thiết kế lại hệ thống theo Clean Architecture và Domain-Driven Design patterns để đảm bảo maintainability, testability và scalability.

## 🎯 Architecture Principles

### 1. **Clean Architecture Layers**
```
┌─────────────────────────────────────────┐
│           Presentation Layer            │  ← HTTP Handlers, Middleware
├─────────────────────────────────────────┤
│           Application Layer             │  ← Use Cases, DTOs
├─────────────────────────────────────────┤
│             Domain Layer                │  ← Entities, Value Objects, Domain Services
├─────────────────────────────────────────┤
│          Infrastructure Layer           │  ← Database, External APIs, Config
└─────────────────────────────────────────┘
```

### 2. **Dependency Direction**
- Outer layers depend on inner layers
- Domain layer has NO dependencies
- Use interfaces for decoupling

## 📁 New Project Structure

```
cmd/
├── api/
│   └── main.go                    # Application entry point

internal/
├── domain/                        # Domain Layer (Core Business Logic)
│   ├── entities/                  # Domain Entities
│   │   ├── key_model_record.go
│   │   ├── season.go
│   │   └── factory.go
│   ├── valueobjects/              # Value Objects
│   │   ├── uuid.go
│   │   ├── stage_status.go
│   │   └── date_range.go
│   ├── repositories/              # Repository Interfaces
│   │   ├── key_model_repository.go
│   │   └── lookup_repository.go
│   ├── services/                  # Domain Services
│   │   └── key_model_domain_service.go
│   └── errors/                    # Domain Errors
│       └── domain_errors.go

├── application/                   # Application Layer (Use Cases)
│   ├── usecases/                  # Use Cases
│   │   ├── key_model/
│   │   │   ├── create_key_model.go
│   │   │   ├── update_key_model.go
│   │   │   ├── get_key_model.go
│   │   │   └── delete_key_model.go
│   │   └── lookup/
│   │       ├── manage_seasons.go
│   │       └── manage_factories.go
│   ├── dto/                       # Data Transfer Objects
│   │   ├── requests/
│   │   │   ├── key_model_request.go
│   │   │   └── lookup_request.go
│   │   └── responses/
│   │       ├── key_model_response.go
│   │       └── lookup_response.go
│   └── ports/                     # Application Interfaces
│       ├── repositories.go
│       └── services.go

├── infrastructure/                # Infrastructure Layer
│   ├── persistence/               # Database Implementation
│   │   ├── sqlserver/
│   │   │   ├── key_model_repository.go
│   │   │   ├── lookup_repository.go
│   │   │   └── migrations/
│   │   └── cache/
│   │       └── redis_cache.go
│   ├── config/                    # Configuration
│   │   ├── config.go
│   │   ├── database.go
│   │   └── environment.go
│   ├── logging/                   # Structured Logging
│   │   ├── logger.go
│   │   └── middleware.go
│   └── monitoring/                # Metrics & Health
│       ├── metrics.go
│       └── health.go

├── presentation/                  # Presentation Layer
│   ├── http/                      # HTTP Handlers
│   │   ├── handlers/
│   │   │   ├── key_model_handler.go
│   │   │   ├── lookup_handler.go
│   │   │   └── health_handler.go
│   │   ├── middleware/
│   │   │   ├── auth.go
│   │   │   ├── validation.go
│   │   │   ├── cors.go
│   │   │   └── rate_limit.go
│   │   └── routes/
│   │       ├── api_v1.go
│   │       └── router.go
│   └── validators/                # Request Validation
│       ├── key_model_validator.go
│       └── common_validator.go

└── shared/                        # Shared Utilities
    ├── constants/
    │   └── constants.go
    ├── utils/
    │   ├── pagination.go
    │   ├── response.go
    │   └── time.go
    └── errors/
        ├── app_errors.go
        └── error_handler.go

pkg/                              # Public Packages
├── logger/
│   └── structured_logger.go
├── database/
│   ├── connection.go
│   └── transaction.go
└── security/
    ├── jwt.go
    └── password.go

tests/                            # Test Files
├── unit/
├── integration/
└── e2e/

configs/                          # Configuration Files
├── config.yaml
├── config.dev.yaml
└── config.prod.yaml

deployments/                      # Deployment Files
├── docker/
│   ├── Dockerfile
│   └── docker-compose.yml
├── k8s/
└── scripts/

docs/                            # Documentation
├── api/
│   └── openapi.yaml
├── architecture/
└── deployment/
```

## 🔧 Key Improvements

### 1. **Dependency Injection Container**
```go
// internal/infrastructure/container/container.go
type Container struct {
    // Repositories
    KeyModelRepo domain.KeyModelRepository
    LookupRepo   domain.LookupRepository
    
    // Use Cases
    CreateKeyModelUC *usecases.CreateKeyModelUseCase
    UpdateKeyModelUC *usecases.UpdateKeyModelUseCase
    
    // Services
    Logger       logger.Logger
    Config       *config.Config
    DB           *gorm.DB
}

func NewContainer() *Container {
    // Wire dependencies
}
```

### 2. **Domain Entities with Business Logic**
```go
// internal/domain/entities/key_model_record.go
type KeyModelRecord struct {
    id           UUID
    colorwayId   string
    materialId   string
    stages       []Stage
    createdAt    time.Time
    updatedAt    time.Time
}

func (k *KeyModelRecord) AdvanceToNextStage() error {
    // Business logic for stage progression
}

func (k *KeyModelRecord) ValidateStageTransition(newStage StageType) error {
    // Domain validation logic
}
```

### 3. **Use Cases with Clear Responsibilities**
```go
// internal/application/usecases/key_model/create_key_model.go
type CreateKeyModelUseCase struct {
    repo   domain.KeyModelRepository
    logger logger.Logger
}

func (uc *CreateKeyModelUseCase) Execute(ctx context.Context, req dto.CreateKeyModelRequest) (*dto.KeyModelResponse, error) {
    // 1. Validate business rules
    // 2. Create domain entity
    // 3. Save to repository
    // 4. Return response
}
```

### 4. **Repository Pattern with Interfaces**
```go
// internal/domain/repositories/key_model_repository.go
type KeyModelRepository interface {
    Create(ctx context.Context, record *entities.KeyModelRecord) error
    GetByID(ctx context.Context, id UUID) (*entities.KeyModelRecord, error)
    Update(ctx context.Context, record *entities.KeyModelRecord) error
    Delete(ctx context.Context, id UUID) error
    FindByFilters(ctx context.Context, filters FilterCriteria) ([]*entities.KeyModelRecord, error)
}
```

## 🛡️ Security & Middleware Stack

### 1. **Authentication Middleware**
- JWT token validation
- Role-based access control
- API key authentication

### 2. **Validation Middleware**
- Request validation using go-playground/validator
- Custom business rule validation
- Sanitization

### 3. **Rate Limiting**
- Per-user rate limiting
- Global rate limiting
- Adaptive rate limiting

## 📊 Monitoring & Observability

### 1. **Structured Logging**
- JSON format logs
- Correlation IDs
- Performance metrics

### 2. **Health Checks**
- Database connectivity
- External service health
- Application metrics

### 3. **Metrics Collection**
- Request/response metrics
- Business metrics
- Performance metrics

## 🧪 Testing Strategy

### 1. **Unit Tests**
- Domain logic testing
- Use case testing
- Repository testing with mocks

### 2. **Integration Tests**
- Database integration
- API endpoint testing
- External service integration

### 3. **E2E Tests**
- Complete workflow testing
- Performance testing
- Load testing

## 🚀 Benefits of New Architecture

✅ **Maintainability** - Clear separation of concerns
✅ **Testability** - Easy to mock and test each layer
✅ **Scalability** - Can scale individual components
✅ **Flexibility** - Easy to change implementations
✅ **Security** - Built-in security patterns
✅ **Performance** - Optimized database access
✅ **Monitoring** - Comprehensive observability
✅ **Documentation** - Self-documenting code structure

## 📋 Migration Plan

1. **Phase 1**: Setup new structure and DI container
2. **Phase 2**: Migrate domain entities and repositories
3. **Phase 3**: Implement use cases and handlers
4. **Phase 4**: Add security and middleware
5. **Phase 5**: Implement monitoring and testing
6. **Phase 6**: Performance optimization and deployment

---

*This architecture follows industry best practices and ensures the system is production-ready, maintainable, and scalable.*
