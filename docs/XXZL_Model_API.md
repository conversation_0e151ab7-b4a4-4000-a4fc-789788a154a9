# XXZL_Model API Documentation

## Overview
API cho bảng `XXZL_Model` chỉ gồm 2 trường:
- `ARTICLE` (varchar(20), NOT NULL)
- `Model` (varchar(50), NOT NULL)

Chức năng:
- Thê<PERSON> mới bản <PERSON>hi (Insert)
- <PERSON><PERSON><PERSON> danh sách/filter (Select)
- <PERSON><PERSON><PERSON> danh sách phân trang (Pagination)
- <PERSON><PERSON><PERSON> danh sách distinct ARTICLE/Model

---

## Database Schema
```sql
CREATE TABLE [dbo].[XXZL_Model](
    [ARTICLE] [varchar](20) NOT NULL,
    [Model] [varchar](50) NOT NULL
)
```

---

## API Endpoints

### 1. Thêm mới bản ghi
**POST** `/api/xxzl-model`

**Request Body:**
```json
{
    "article": "A001",
    "model": "M001"
}
```

**Response:**
```json
{
    "code": 200,
    "data": {
        "article": "A001",
        "model": "M001"
    },
    "message": "success"
}
```

---

### 2. <PERSON><PERSON><PERSON> danh sách/filter
**GET** `/api/xxzl-model`

**Query Parameters:**
- `article` (optional): Lọc theo ARTICLE (LIKE pattern)
- `model` (optional): Lọc theo Model (LIKE pattern)

**Ví dụ:**
```
GET /api/xxzl-model?article=A00&model=M00
```

**Response:**
```json
{
    "code": 200,
    "data": [
        { "article": "A001", "model": "M001" },
        { "article": "A002", "model": "M002" }
    ],
    "message": "success"
}
```

---

### 3. Lấy danh sách phân trang
**GET** `/api/xxzl-model/paginated`

**Query Parameters:**
- `article` (optional): Lọc theo ARTICLE (LIKE pattern)
- `model` (optional): Lọc theo Model (LIKE pattern)
- `page` (optional): Số trang (default: 1)
- `pageSize` (optional): Số bản ghi/trang (default: 10, max: 1000)

**Ví dụ:**
```
GET /api/xxzl-model/paginated?page=1&pageSize=10&article=A00
```

**Response:**
```json
{
    "code": 200,
    "data": {
        "data": [
            { "article": "A001", "model": "M001" },
            { "article": "A002", "model": "M002" }
        ],
        "totalRows": 20,
        "totalPages": 2,
        "page": 1,
        "pageSize": 10,
        "hasNext": true,
        "hasPrev": false,
        "message": "success",
        "success": true
    },
    "message": "success"
}
```

---

### 4. Lấy danh sách distinct ARTICLE
**GET** `/api/xxzl-model/articles`

**Response:**
```json
{
    "code": 200,
    "data": ["A001", "A002", "A003"],
    "message": "success"
}
```

---

### 5. Lấy danh sách distinct Model
**GET** `/api/xxzl-model/models`

**Response:**
```json
{
    "code": 200,
    "data": ["M001", "M002", "M003"],
    "message": "success"
}
```

---

## Error Handling

Tất cả endpoint đều trả về lỗi chuẩn:
```json
{
    "code": 400,
    "data": null,
    "message": "Invalid request parameters"
}
```

---

## Example curl

**Thêm mới:**
```bash
curl -X POST http://localhost:8080/api/xxzl-model \
  -H "Content-Type: application/json" \
  -d '{"article":"A001","model":"M001"}'
```

**Lấy danh sách:**
```bash
curl "http://localhost:8080/api/xxzl-model?article=A00"
```

**Lấy phân trang:**
```bash
curl "http://localhost:8080/api/xxzl-model/paginated?page=1&pageSize=10"
```

**Lấy distinct:**
```bash
curl "http://localhost:8080/api/xxzl-model/articles"
curl "http://localhost:8080/api/xxzl-model/models"
``` 