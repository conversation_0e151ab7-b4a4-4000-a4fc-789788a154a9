openapi: 3.0.3
info:
  title: Key Model Tracking API
  description: |
    A comprehensive API for tracking key model development through various stages including Product Creation, Commercial, and Manufacturing phases.
    
    ## Features
    - Complete CRUD operations for key model records
    - Stage-based workflow management
    - Advanced filtering and pagination
    - Lookup table management
    - Authentication and authorization
    - Real-time monitoring and metrics
    
    ## Authentication
    This API uses JWT Bearer tokens for authentication. Include the token in the Authorization header:
    ```
    Authorization: Bearer <your-jwt-token>
    ```
    
    ## Rate Limiting
    API requests are rate limited to 1000 requests per hour per user.
    
    ## Error Handling
    All errors follow a consistent format with appropriate HTTP status codes and detailed error messages.
  version: 2.0.0
  contact:
    name: API Support
    email: <EMAIL>
    url: https://company.com/support
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: https://company.com/terms

servers:
  - url: https://api.company.com/v2
    description: Production server
  - url: https://staging-api.company.com/v2
    description: Staging server
  - url: http://localhost:8004/api/v2
    description: Development server

security:
  - BearerAuth: []
  - ApiKeyAuth: []

paths:
  /key-model-records:
    get:
      summary: List key model records
      description: Retrieve a list of key model records with optional filtering and pagination
      tags:
        - Key Model Records
      parameters:
        - $ref: '#/components/parameters/ColorwayIdFilter'
        - $ref: '#/components/parameters/MaterialIdFilter'
        - $ref: '#/components/parameters/KeyModelTypeFilter'
        - $ref: '#/components/parameters/ProductNameFilter'
        - $ref: '#/components/parameters/FtyFilter'
        - $ref: '#/components/parameters/IsdFilter'
        - $ref: '#/components/parameters/SeasonFilter'
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
        - $ref: '#/components/parameters/SortBy'
        - $ref: '#/components/parameters/SortOrder'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KeyModelRecordListResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
    
    post:
      summary: Create key model record
      description: Create a new key model record
      tags:
        - Key Model Records
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateKeyModelRecordRequest'
      responses:
        '201':
          description: Key model record created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KeyModelRecordResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /key-model-records/{id}:
    get:
      summary: Get key model record by ID
      description: Retrieve a specific key model record by its ID
      tags:
        - Key Model Records
      parameters:
        - $ref: '#/components/parameters/KeyModelRecordId'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KeyModelRecordResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
    
    put:
      summary: Update key model record
      description: Update an existing key model record
      tags:
        - Key Model Records
      parameters:
        - $ref: '#/components/parameters/KeyModelRecordId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateKeyModelRecordRequest'
      responses:
        '200':
          description: Key model record updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KeyModelRecordResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    
    delete:
      summary: Delete key model record
      description: Delete a key model record
      tags:
        - Key Model Records
      parameters:
        - $ref: '#/components/parameters/KeyModelRecordId'
      responses:
        '200':
          description: Key model record deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /key-model-records/{id}/stages/{stageType}/advance:
    post:
      summary: Advance to next stage
      description: Advance a key model record to the next stage
      tags:
        - Key Model Records
        - Stages
      parameters:
        - $ref: '#/components/parameters/KeyModelRecordId'
        - $ref: '#/components/parameters/StageType'
      responses:
        '200':
          description: Stage advanced successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KeyModelRecordResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /auth/login:
    post:
      summary: User login
      description: Authenticate user and return JWT token
      tags:
        - Authentication
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /auth/refresh:
    post:
      summary: Refresh token
      description: Refresh JWT token using refresh token
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenRequest'
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /health:
    get:
      summary: Health check
      description: Check the health status of the API and its dependencies
      tags:
        - System
      security: []
      responses:
        '200':
          description: System is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
        '503':
          description: System is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'

  /metrics:
    get:
      summary: Prometheus metrics
      description: Prometheus metrics endpoint
      tags:
        - System
      security:
        - ApiKeyAuth: []
      responses:
        '200':
          description: Metrics data
          content:
            text/plain:
              schema:
                type: string

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key

  parameters:
    KeyModelRecordId:
      name: id
      in: path
      required: true
      description: Key model record ID
      schema:
        type: string
        format: uuid
        example: "550e8400-e29b-41d4-a716-************"
    
    StageType:
      name: stageType
      in: path
      required: true
      description: Stage type
      schema:
        $ref: '#/components/schemas/StageType'
    
    ColorwayIdFilter:
      name: colorwayId
      in: query
      description: Filter by colorway ID
      schema:
        type: string
        example: "CW001"
    
    MaterialIdFilter:
      name: materialId
      in: query
      description: Filter by material ID
      schema:
        type: string
        example: "MAT001"
    
    KeyModelTypeFilter:
      name: keyModelType
      in: query
      description: Filter by key model type
      schema:
        type: string
        example: "DESIGN"
    
    ProductNameFilter:
      name: productName
      in: query
      description: Filter by product name (partial match)
      schema:
        type: string
        example: "Nike"
    
    FtyFilter:
      name: fty
      in: query
      description: Filter by factory code
      schema:
        type: string
        example: "LTN"
    
    IsdFilter:
      name: isd
      in: query
      description: Filter by ISD
      schema:
        type: string
        example: "ISD001"
    
    SeasonFilter:
      name: season
      in: query
      description: Filter by season
      schema:
        type: string
        example: "S25"
    
    PageNumber:
      name: pageNumber
      in: query
      description: Page number (1-based)
      schema:
        type: integer
        minimum: 1
        default: 1
        example: 1
    
    PageSize:
      name: pageSize
      in: query
      description: Number of items per page
      schema:
        type: integer
        minimum: 1
        maximum: 1000
        default: 20
        example: 20
    
    SortBy:
      name: sortBy
      in: query
      description: Field to sort by
      schema:
        type: string
        enum: [createdAt, updatedAt, productName, colorwayId, materialId]
        default: createdAt
    
    SortOrder:
      name: sortOrder
      in: query
      description: Sort order
      schema:
        type: string
        enum: [asc, desc]
        default: desc

  schemas:
    KeyModelRecord:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier
          example: "550e8400-e29b-41d4-a716-************"
        colorwayId:
          type: string
          description: Colorway identifier
          example: "CW001"
        materialId:
          type: string
          description: Material identifier
          example: "MAT001"
        keyModelType:
          type: string
          description: Type of key model
          example: "DESIGN"
        productName:
          type: string
          description: Product name
          example: "Nike Air Max 270"
        fty:
          type: string
          description: Factory code
          example: "LTN"
        quantity:
          type: integer
          nullable: true
          description: Production quantity
          example: 1000
        isd:
          type: string
          description: ISD identifier
          example: "ISD001"
        season:
          type: string
          description: Season code
          example: "S25"
        currentStage:
          $ref: '#/components/schemas/StageType'
        stages:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/Stage'
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
        createdBy:
          type: string
          description: User who created the record
          example: "user123"
        updatedBy:
          type: string
          description: User who last updated the record
          example: "user123"
        isActive:
          type: boolean
          description: Whether the record is active
          example: true

    Stage:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/StageType'
        status:
          $ref: '#/components/schemas/StageStatus'
        startedAt:
          type: string
          format: date-time
          nullable: true
          description: When the stage was started
        completedAt:
          type: string
          format: date-time
          nullable: true
          description: When the stage was completed
        data:
          type: object
          additionalProperties: true
          description: Stage-specific data
        comments:
          type: string
          description: Comments for this stage
        updatedBy:
          type: string
          description: User who last updated this stage

    StageType:
      type: string
      enum:
        - PRODUCT_CREATION
        - COMMERCIAL_EST
        - COMMERCIAL_FST
        - MANUFACTURING
        - COMPLETED
      description: Type of stage in the key model tracking process

    StageStatus:
      type: string
      enum:
        - NOT_STARTED
        - IN_PROGRESS
        - COMPLETED
        - FAILED
      description: Status of a stage

    CreateKeyModelRecordRequest:
      type: object
      required:
        - colorwayId
        - materialId
        - keyModelType
        - productName
        - fty
        - isd
        - season
      properties:
        colorwayId:
          type: string
          example: "CW001"
        materialId:
          type: string
          example: "MAT001"
        keyModelType:
          type: string
          example: "DESIGN"
        productName:
          type: string
          example: "Nike Air Max 270"
        fty:
          type: string
          example: "LTN"
        quantity:
          type: integer
          nullable: true
          example: 1000
        isd:
          type: string
          example: "ISD001"
        season:
          type: string
          example: "S25"
        poReceivedDate:
          type: string
          format: date-time
          nullable: true
        userId:
          type: string
          example: "user123"

    UpdateKeyModelRecordRequest:
      type: object
      properties:
        colorwayId:
          type: string
          example: "CW001"
        materialId:
          type: string
          example: "MAT001"
        keyModelType:
          type: string
          example: "DESIGN"
        productName:
          type: string
          example: "Nike Air Max 270 Updated"
        fty:
          type: string
          example: "LTN"
        quantity:
          type: integer
          nullable: true
          example: 1200
        isd:
          type: string
          example: "ISD001"
        season:
          type: string
          example: "S25"
        userId:
          type: string
          example: "user123"

    LoginRequest:
      type: object
      required:
        - username
        - password
      properties:
        username:
          type: string
          example: "user123"
        password:
          type: string
          format: password
          example: "password123"

    RefreshTokenRequest:
      type: object
      required:
        - refreshToken
      properties:
        refreshToken:
          type: string
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

    LoginResponse:
      type: object
      properties:
        accessToken:
          type: string
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        refreshToken:
          type: string
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        tokenType:
          type: string
          example: "Bearer"
        expiresIn:
          type: integer
          example: 3600
        expiresAt:
          type: string
          format: date-time

    HealthResponse:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, unhealthy]
          example: "healthy"
        timestamp:
          type: string
          format: date-time
        checks:
          type: object
          additionalProperties:
            type: string
          example:
            database: "healthy"
            cache: "healthy"
            external_api: "healthy"
        version:
          type: string
          example: "2.0.0"
        uptime:
          type: string
          example: "2h30m15s"

    StandardResponse:
      type: object
      properties:
        code:
          type: integer
          description: HTTP status code
          example: 200
        message:
          type: string
          description: Response message
          example: "success"
        data:
          description: Response data
        meta:
          $ref: '#/components/schemas/PaginationMeta'

    KeyModelRecordResponse:
      allOf:
        - $ref: '#/components/schemas/StandardResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/KeyModelRecord'

    KeyModelRecordListResponse:
      allOf:
        - $ref: '#/components/schemas/StandardResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/KeyModelRecord'

    SuccessResponse:
      allOf:
        - $ref: '#/components/schemas/StandardResponse'
        - type: object
          properties:
            data:
              type: object
              nullable: true

    PaginationMeta:
      type: object
      properties:
        page:
          type: integer
          example: 1
        pageSize:
          type: integer
          example: 20
        total:
          type: integer
          example: 100
        totalPages:
          type: integer
          example: 5

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: HTTP status code
        message:
          type: string
          description: Error message
        error:
          $ref: '#/components/schemas/ErrorDetail'

    ErrorDetail:
      type: object
      properties:
        type:
          type: string
          enum:
            - VALIDATION_ERROR
            - DOMAIN_ERROR
            - NOT_FOUND_ERROR
            - CONFLICT_ERROR
            - UNAUTHORIZED_ERROR
            - FORBIDDEN_ERROR
            - INTERNAL_ERROR
            - EXTERNAL_ERROR
        message:
          type: string
        details:
          type: object
          additionalProperties: true
        code:
          type: string

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 400
            message: "Validation failed"
            error:
              type: "VALIDATION_ERROR"
              message: "Invalid request data"
              details:
                field_errors:
                  - field: "colorwayId"
                    message: "colorwayId is required"

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 401
            message: "Authentication required"
            error:
              type: "UNAUTHORIZED_ERROR"
              message: "Invalid or missing authentication token"

    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 403
            message: "Access denied"
            error:
              type: "FORBIDDEN_ERROR"
              message: "Insufficient permissions"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 404
            message: "Resource not found"
            error:
              type: "NOT_FOUND_ERROR"
              message: "Key model record with identifier 'abc123' not found"

    Conflict:
      description: Conflict
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 409
            message: "Resource conflict"
            error:
              type: "CONFLICT_ERROR"
              message: "Key model record with this colorway and material already exists"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 500
            message: "Internal server error"
            error:
              type: "INTERNAL_ERROR"
              message: "An unexpected error occurred"

tags:
  - name: Key Model Records
    description: Operations related to key model records
  - name: Stages
    description: Operations related to stage management
  - name: Authentication
    description: Authentication and authorization operations
  - name: System
    description: System health and monitoring endpoints
