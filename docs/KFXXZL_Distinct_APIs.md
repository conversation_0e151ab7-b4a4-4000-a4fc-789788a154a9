# KFXXZL Distinct Value APIs

This document describes the 4 new APIs that have been implemented to retrieve distinct values from the `kfxxzl` table in the ERP database.

## Overview

These APIs provide access to distinct values for:
- Colorway IDs (DEVCODE)
- Material IDs (ARTICLE) 
- Seasons (jijie)
- Factories (kfcq)

**Note:** All APIs automatically filter out NULL values to ensure data integrity.

## API Endpoints

### 1. Get Distinct Colorways

**Endpoint:** `GET /api/colorways`

**Description:** Retrieves all distinct colorway IDs from the kfxxzl table

**SQL Query:**
```sql
SELECT DISTINCT DEVCODE AS ColorwayId 
FROM [ERP_23_9].[LIY_ERP].[dbo].[kfxxzl]
WHERE DEVCODE IS NOT NULL
```

**Response:**
```json
{
  "code": 200,
  "message": "success",
  "data": ["COLOR001", "COLOR002", "COLOR003"]
}
```

### 2. Get Distinct Materials

**Endpoint:** `GET /api/materials`

**Description:** Retrieves all distinct material IDs from the kfxxzl table

**SQL Query:**
```sql
SELECT DISTINCT ARTICLE AS MaterialId 
FROM [ERP_23_9].[LIY_ERP].[dbo].[kfxxzl]
WHERE ARTICLE IS NOT NULL
```

**Response:**
```json
{
  "code": 200,
  "message": "success",
  "data": ["MAT001", "MAT002", "MAT003"]
}
```

### 3. Get Distinct Seasons

**Endpoint:** `GET /api/seasons`

**Description:** Retrieves all distinct seasons from the kfxxzl table

**SQL Query:**
```sql
SELECT DISTINCT jijie as Season 
FROM [ERP_23_9].[LIY_ERP].[dbo].[kfxxzl]
WHERE jijie IS NOT NULL
```

**Response:**
```json
{
  "code": 200,
  "message": "success",
  "data": ["Spring", "Summer", "Fall", "Winter"]
}
```

### 4. Get Distinct Factories

**Endpoint:** `GET /api/factories`

**Description:** Retrieves all distinct factories from the kfxxzl table

**SQL Query:**
```sql
SELECT DISTINCT kfcq as factory 
FROM [ERP_23_9].[LIY_ERP].[dbo].[kfxxzl]
WHERE kfcq IS NOT NULL
```

**Response:**
```json
{
  "code": 200,
  "message": "success",
  "data": ["FACTORY001", "FACTORY002", "FACTORY003"]
}
```

## Implementation Details

### Service Layer
- **File:** `internal/api/services/common_service.go`
- **Methods Added:**
  - `GetDistinctColorways()`
  - `GetDistinctMaterials()`
  - `GetDistinctSeasons()`
  - `GetDistinctFactories()`

### Controller Layer
- **File:** `internal/api/controllers/common_controller.go`
- **Methods Added:**
  - `GetDistinctColorways()`
  - `GetDistinctMaterials()`
  - `GetDistinctSeasons()`
  - `GetDistinctFactories()`

### Router Layer
- **File:** `internal/api/routers/v1/common.go`
- **Routes Added:**
  - `GET /api/colorways`
  - `GET /api/materials`
  - `GET /api/seasons`
  - `GET /api/factories`

## Database Connection

All APIs use the existing database connection pattern:
- Connection established via `database.DatabaseConnection()`
- Raw SQL queries executed using GORM's `Raw()` method
- Results scanned into string slices
- Proper error handling and connection cleanup

## NULL Value Handling

- All queries include `WHERE column IS NOT NULL` to filter out NULL values
- This prevents SQL scan errors when converting NULL to string
- Ensures only valid, non-null data is returned
- Improves data quality and API reliability

## Error Handling

- Database connection errors return appropriate error messages
- Query execution errors are logged and returned to the client
- All endpoints follow the existing response pattern using `response.OkWithData()` and `response.FailWithMessage()`

## Testing

To test these APIs:

1. **Build the application:**
   ```bash
   make build
   ```

2. **Run the application:**
   ```bash
   make run
   ```

3. **Test the endpoints:**
   ```bash
   curl http://localhost:8081/api/colorways
   curl http://localhost:8081/api/materials
   curl http://localhost:8081/api/seasons
   curl http://localhost:8081/api/factories
   ```

## Notes

- All APIs return distinct values only (no duplicates)
- NULL values are automatically filtered out for data integrity
- Results are returned as simple string arrays
- No authentication required for these read-only endpoints
- APIs follow the existing codebase patterns and conventions
- Swagger documentation annotations are included for API documentation
