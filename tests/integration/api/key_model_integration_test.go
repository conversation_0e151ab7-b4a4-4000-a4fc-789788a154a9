package api_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"web-api/internal/infrastructure/container"
	"web-api/internal/presentation/http/routes"
	"web-api/internal/shared/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// KeyModelIntegrationTestSuite provides integration tests for Key Model API
type KeyModelIntegrationTestSuite struct {
	suite.Suite
	router    *gin.Engine
	container *container.Container
	ctx       context.Context
}

// SetupSuite runs once before all tests
func (suite *KeyModelIntegrationTestSuite) SetupSuite() {
	gin.SetMode(gin.TestMode)
	suite.ctx = context.Background()
	
	// Initialize test container
	testContainer, err := container.NewTestContainer()
	require.NoError(suite.T(), err)
	suite.container = testContainer
	
	// Setup router with test container
	suite.router = routes.SetupRoutes(testContainer)
}

// TearDownSuite runs once after all tests
func (suite *KeyModelIntegrationTestSuite) TearDownSuite() {
	if suite.container != nil {
		suite.container.Cleanup()
	}
}

// SetupTest runs before each test
func (suite *KeyModelIntegrationTestSuite) SetupTest() {
	// Clean up test data before each test
	suite.cleanupTestData()
}

// TearDownTest runs after each test
func (suite *KeyModelIntegrationTestSuite) TearDownTest() {
	// Clean up test data after each test
	suite.cleanupTestData()
}

func (suite *KeyModelIntegrationTestSuite) cleanupTestData() {
	// Implementation depends on your database setup
	// For example, truncate test tables or delete test records
}

// TestCreateKeyModelRecord tests the creation of a key model record
func (suite *KeyModelIntegrationTestSuite) TestCreateKeyModelRecord() {
	tests := []struct {
		name           string
		payload        map[string]interface{}
		expectedStatus int
		expectedFields map[string]interface{}
	}{
		{
			name: "successful creation",
			payload: map[string]interface{}{
				"colorwayId":   "CW001",
				"materialId":   "MAT001",
				"keyModelType": "DESIGN",
				"productName":  "Test Product",
				"fty":          "LTN",
				"isd":          "ISD001",
				"season":       "S25",
				"quantity":     1000,
				"userId":       "user123",
			},
			expectedStatus: http.StatusCreated,
			expectedFields: map[string]interface{}{
				"colorwayId":   "CW001",
				"materialId":   "MAT001",
				"keyModelType": "DESIGN",
				"productName":  "Test Product",
			},
		},
		{
			name: "missing required fields",
			payload: map[string]interface{}{
				"colorwayId": "CW001",
				// Missing materialId
				"keyModelType": "DESIGN",
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "invalid data types",
			payload: map[string]interface{}{
				"colorwayId":   "CW001",
				"materialId":   "MAT001",
				"keyModelType": "DESIGN",
				"productName":  "Test Product",
				"fty":          "LTN",
				"isd":          "ISD001",
				"season":       "S25",
				"quantity":     "invalid", // Should be integer
			},
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			// Prepare request
			jsonPayload, err := json.Marshal(tt.payload)
			require.NoError(suite.T(), err)

			req, err := http.NewRequest("POST", "/api/key-model-records", bytes.NewBuffer(jsonPayload))
			require.NoError(suite.T(), err)
			req.Header.Set("Content-Type", "application/json")

			// Execute request
			w := httptest.NewRecorder()
			suite.router.ServeHTTP(w, req)

			// Assert response
			assert.Equal(suite.T(), tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusCreated {
				var response utils.StandardResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(suite.T(), err)

				assert.Equal(suite.T(), http.StatusCreated, response.Code)
				assert.Equal(suite.T(), "success", response.Message)
				assert.NotNil(suite.T(), response.Data)

				// Verify expected fields
				data, ok := response.Data.(map[string]interface{})
				require.True(suite.T(), ok)

				for field, expectedValue := range tt.expectedFields {
					actualValue, exists := data[field]
					assert.True(suite.T(), exists, "Field %s should exist", field)
					assert.Equal(suite.T(), expectedValue, actualValue, "Field %s should match", field)
				}

				// Verify ID is generated
				id, exists := data["id"]
				assert.True(suite.T(), exists)
				assert.NotEmpty(suite.T(), id)
			}
		})
	}
}

// TestGetKeyModelRecord tests retrieving a key model record
func (suite *KeyModelIntegrationTestSuite) TestGetKeyModelRecord() {
	// First, create a record
	recordID := suite.createTestRecord()

	tests := []struct {
		name           string
		recordID       string
		expectedStatus int
	}{
		{
			name:           "successful retrieval",
			recordID:       recordID,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "record not found",
			recordID:       "non-existent-id",
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "invalid UUID format",
			recordID:       "invalid-uuid",
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			req, err := http.NewRequest("GET", fmt.Sprintf("/api/key-model-records/%s", tt.recordID), nil)
			require.NoError(suite.T(), err)

			w := httptest.NewRecorder()
			suite.router.ServeHTTP(w, req)

			assert.Equal(suite.T(), tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response utils.StandardResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(suite.T(), err)

				assert.Equal(suite.T(), http.StatusOK, response.Code)
				assert.NotNil(suite.T(), response.Data)
			}
		})
	}
}

// TestUpdateKeyModelRecord tests updating a key model record
func (suite *KeyModelIntegrationTestSuite) TestUpdateKeyModelRecord() {
	// First, create a record
	recordID := suite.createTestRecord()

	tests := []struct {
		name           string
		recordID       string
		payload        map[string]interface{}
		expectedStatus int
	}{
		{
			name:     "successful update",
			recordID: recordID,
			payload: map[string]interface{}{
				"productName": "Updated Product Name",
				"quantity":    2000,
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:     "partial update",
			recordID: recordID,
			payload: map[string]interface{}{
				"quantity": 1500,
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:     "record not found",
			recordID: "non-existent-id",
			payload: map[string]interface{}{
				"productName": "Updated Product Name",
			},
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			jsonPayload, err := json.Marshal(tt.payload)
			require.NoError(suite.T(), err)

			req, err := http.NewRequest("PUT", fmt.Sprintf("/api/key-model-records/%s", tt.recordID), bytes.NewBuffer(jsonPayload))
			require.NoError(suite.T(), err)
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			suite.router.ServeHTTP(w, req)

			assert.Equal(suite.T(), tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response utils.StandardResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(suite.T(), err)

				assert.Equal(suite.T(), http.StatusOK, response.Code)
				assert.NotNil(suite.T(), response.Data)

				// Verify updated fields
				data, ok := response.Data.(map[string]interface{})
				require.True(suite.T(), ok)

				for field, expectedValue := range tt.payload {
					actualValue, exists := data[field]
					assert.True(suite.T(), exists, "Field %s should exist", field)
					assert.Equal(suite.T(), expectedValue, actualValue, "Field %s should be updated", field)
				}
			}
		})
	}
}

// TestDeleteKeyModelRecord tests deleting a key model record
func (suite *KeyModelIntegrationTestSuite) TestDeleteKeyModelRecord() {
	// First, create a record
	recordID := suite.createTestRecord()

	tests := []struct {
		name           string
		recordID       string
		expectedStatus int
	}{
		{
			name:           "successful deletion",
			recordID:       recordID,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "record not found",
			recordID:       "non-existent-id",
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			req, err := http.NewRequest("DELETE", fmt.Sprintf("/api/key-model-records/%s", tt.recordID), nil)
			require.NoError(suite.T(), err)

			w := httptest.NewRecorder()
			suite.router.ServeHTTP(w, req)

			assert.Equal(suite.T(), tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				// Verify record is actually deleted by trying to get it
				getReq, err := http.NewRequest("GET", fmt.Sprintf("/api/key-model-records/%s", tt.recordID), nil)
				require.NoError(suite.T(), err)

				getW := httptest.NewRecorder()
				suite.router.ServeHTTP(getW, getReq)

				assert.Equal(suite.T(), http.StatusNotFound, getW.Code)
			}
		})
	}
}

// TestListKeyModelRecords tests listing key model records with filters
func (suite *KeyModelIntegrationTestSuite) TestListKeyModelRecords() {
	// Create multiple test records
	suite.createMultipleTestRecords()

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectedCount  int
	}{
		{
			name:           "list all records",
			queryParams:    "",
			expectedStatus: http.StatusOK,
			expectedCount:  3, // Assuming we created 3 test records
		},
		{
			name:           "filter by colorway",
			queryParams:    "?colorwayId=CW001",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "filter by season",
			queryParams:    "?season=S25",
			expectedStatus: http.StatusOK,
			expectedCount:  2,
		},
		{
			name:           "pagination",
			queryParams:    "?pageNumber=1&pageSize=2",
			expectedStatus: http.StatusOK,
			expectedCount:  2,
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			req, err := http.NewRequest("GET", "/api/key-model-records"+tt.queryParams, nil)
			require.NoError(suite.T(), err)

			w := httptest.NewRecorder()
			suite.router.ServeHTTP(w, req)

			assert.Equal(suite.T(), tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response utils.StandardResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				require.NoError(suite.T(), err)

				assert.Equal(suite.T(), http.StatusOK, response.Code)
				assert.NotNil(suite.T(), response.Data)

				// Verify record count
				data, ok := response.Data.([]interface{})
				require.True(suite.T(), ok)
				assert.Len(suite.T(), data, tt.expectedCount)
			}
		})
	}
}

// Helper methods

func (suite *KeyModelIntegrationTestSuite) createTestRecord() string {
	payload := map[string]interface{}{
		"colorwayId":   "CW001",
		"materialId":   "MAT001",
		"keyModelType": "DESIGN",
		"productName":  "Test Product",
		"fty":          "LTN",
		"isd":          "ISD001",
		"season":       "S25",
		"quantity":     1000,
		"userId":       "user123",
	}

	jsonPayload, err := json.Marshal(payload)
	require.NoError(suite.T(), err)

	req, err := http.NewRequest("POST", "/api/key-model-records", bytes.NewBuffer(jsonPayload))
	require.NoError(suite.T(), err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	require.Equal(suite.T(), http.StatusCreated, w.Code)

	var response utils.StandardResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)

	data, ok := response.Data.(map[string]interface{})
	require.True(suite.T(), ok)

	id, exists := data["id"]
	require.True(suite.T(), exists)

	return id.(string)
}

func (suite *KeyModelIntegrationTestSuite) createMultipleTestRecords() {
	records := []map[string]interface{}{
		{
			"colorwayId":   "CW001",
			"materialId":   "MAT001",
			"keyModelType": "DESIGN",
			"productName":  "Product 1",
			"fty":          "LTN",
			"isd":          "ISD001",
			"season":       "S25",
			"quantity":     1000,
			"userId":       "user123",
		},
		{
			"colorwayId":   "CW002",
			"materialId":   "MAT002",
			"keyModelType": "PRODUCTION",
			"productName":  "Product 2",
			"fty":          "VTN",
			"isd":          "ISD002",
			"season":       "S25",
			"quantity":     1500,
			"userId":       "user123",
		},
		{
			"colorwayId":   "CW003",
			"materialId":   "MAT003",
			"keyModelType": "DESIGN",
			"productName":  "Product 3",
			"fty":          "LTN",
			"isd":          "ISD003",
			"season":       "F25",
			"quantity":     2000,
			"userId":       "user123",
		},
	}

	for _, record := range records {
		jsonPayload, err := json.Marshal(record)
		require.NoError(suite.T(), err)

		req, err := http.NewRequest("POST", "/api/key-model-records", bytes.NewBuffer(jsonPayload))
		require.NoError(suite.T(), err)
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		require.Equal(suite.T(), http.StatusCreated, w.Code)
	}
}

// TestKeyModelIntegrationTestSuite runs the integration test suite
func TestKeyModelIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(KeyModelIntegrationTestSuite))
}

// Performance tests
func (suite *KeyModelIntegrationTestSuite) TestPerformance_CreateManyRecords() {
	start := time.Now()
	
	for i := 0; i < 100; i++ {
		payload := map[string]interface{}{
			"colorwayId":   fmt.Sprintf("CW%03d", i),
			"materialId":   fmt.Sprintf("MAT%03d", i),
			"keyModelType": "DESIGN",
			"productName":  fmt.Sprintf("Product %d", i),
			"fty":          "LTN",
			"isd":          fmt.Sprintf("ISD%03d", i),
			"season":       "S25",
			"quantity":     1000 + i,
			"userId":       "user123",
		}

		jsonPayload, err := json.Marshal(payload)
		require.NoError(suite.T(), err)

		req, err := http.NewRequest("POST", "/api/key-model-records", bytes.NewBuffer(jsonPayload))
		require.NoError(suite.T(), err)
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(suite.T(), http.StatusCreated, w.Code)
	}
	
	duration := time.Since(start)
	suite.T().Logf("Created 100 records in %v (avg: %v per record)", duration, duration/100)
	
	// Assert reasonable performance (adjust threshold as needed)
	assert.Less(suite.T(), duration, 10*time.Second, "Creating 100 records should take less than 10 seconds")
}
