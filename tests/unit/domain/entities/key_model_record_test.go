package entities_test

import (
	"testing"
	"time"

	"web-api/internal/domain/entities"
	"web-api/internal/shared/errors"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewKeyModelRecord(t *testing.T) {
	tests := []struct {
		name        string
		colorwayId  string
		materialId  string
		keyModelType string
		productName string
		fty         string
		isd         string
		season      string
		createdBy   string
		quantity    *int
		poReceivedDate *time.Time
		expectError bool
		errorType   string
	}{
		{
			name:         "valid key model record",
			colorwayId:   "CW001",
			materialId:   "MAT001",
			keyModelType: "DESIGN",
			productName:  "Test Product",
			fty:          "LTN",
			isd:          "ISD001",
			season:       "S25",
			createdBy:    "user123",
			quantity:     intPtr(1000),
			poReceivedDate: timePtr(time.Now()),
			expectError:  false,
		},
		{
			name:         "missing colorway ID",
			colorwayId:   "",
			materialId:   "MAT001",
			keyModelType: "DESIGN",
			productName:  "Test Product",
			fty:          "LTN",
			isd:          "ISD001",
			season:       "S25",
			createdBy:    "user123",
			expectError:  true,
			errorType:    "validation",
		},
		{
			name:         "missing material ID",
			colorwayId:   "CW001",
			materialId:   "",
			keyModelType: "DESIGN",
			productName:  "Test Product",
			fty:          "LTN",
			isd:          "ISD001",
			season:       "S25",
			createdBy:    "user123",
			expectError:  true,
			errorType:    "validation",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record, err := entities.NewKeyModelRecord(
				tt.colorwayId,
				tt.materialId,
				tt.keyModelType,
				tt.productName,
				tt.fty,
				tt.isd,
				tt.season,
				tt.createdBy,
				tt.quantity,
				tt.poReceivedDate,
			)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, record)
				
				if tt.errorType == "validation" {
					var appErr *errors.AppError
					require.ErrorAs(t, err, &appErr)
					assert.Equal(t, errors.ErrorTypeValidation, appErr.Type)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, record)
				
				// Verify basic properties
				assert.Equal(t, tt.colorwayId, record.GetColorwayId())
				assert.Equal(t, tt.materialId, record.GetMaterialId())
				assert.Equal(t, entities.StageProductCreation, record.GetCurrentStage())
				assert.True(t, record.IsActive())
				
				// Verify initial stage is created
				stage := record.GetStage(entities.StageProductCreation)
				assert.NotNil(t, stage)
				assert.Equal(t, entities.StatusInProgress, stage.Status)
				assert.NotNil(t, stage.StartedAt)
			}
		})
	}
}

func TestKeyModelRecord_AdvanceToNextStage(t *testing.T) {
	// Create a test record
	record, err := entities.NewKeyModelRecord(
		"CW001", "MAT001", "DESIGN", "Test Product",
		"LTN", "ISD001", "S25", "user123", nil, nil,
	)
	require.NoError(t, err)

	tests := []struct {
		name           string
		currentStage   entities.StageType
		stageStatus    entities.StageStatus
		expectedNext   entities.StageType
		expectError    bool
		errorMessage   string
	}{
		{
			name:         "advance from product creation to commercial EST",
			currentStage: entities.StageProductCreation,
			stageStatus:  entities.StatusCompleted,
			expectedNext: entities.StageCommercialEST,
			expectError:  false,
		},
		{
			name:         "cannot advance incomplete stage",
			currentStage: entities.StageProductCreation,
			stageStatus:  entities.StatusInProgress,
			expectError:  true,
			errorMessage: "current stage must be completed before advancing",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup stage status
			stage := record.GetStage(tt.currentStage)
			stage.Status = tt.stageStatus
			if tt.stageStatus == entities.StatusCompleted {
				now := time.Now()
				stage.CompletedAt = &now
			}

			err := record.AdvanceToNextStage("user123")

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMessage)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedNext, record.GetCurrentStage())
				
				// Verify new stage is initialized
				newStage := record.GetStage(tt.expectedNext)
				assert.NotNil(t, newStage)
				assert.Equal(t, entities.StatusInProgress, newStage.Status)
			}
		})
	}
}

func TestKeyModelRecord_UpdateStageData(t *testing.T) {
	record, err := entities.NewKeyModelRecord(
		"CW001", "MAT001", "DESIGN", "Test Product",
		"LTN", "ISD001", "S25", "user123", nil, nil,
	)
	require.NoError(t, err)

	tests := []struct {
		name        string
		stageType   entities.StageType
		data        map[string]interface{}
		expectError bool
	}{
		{
			name:      "update current stage data",
			stageType: entities.StageProductCreation,
			data: map[string]interface{}{
				"poReceived":     true,
				"pfcValidated":   false,
				"designFeature": "Advanced cushioning",
			},
			expectError: false,
		},
		{
			name:        "update non-existent stage",
			stageType:   entities.StageCommercialEST,
			data:        map[string]interface{}{"test": "value"},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := record.UpdateStageData(tt.stageType, tt.data, "user123")

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				
				stage := record.GetStage(tt.stageType)
				assert.NotNil(t, stage)
				
				for key, expectedValue := range tt.data {
					actualValue, exists := stage.Data[key]
					assert.True(t, exists, "Key %s should exist in stage data", key)
					assert.Equal(t, expectedValue, actualValue)
				}
				
				assert.Equal(t, "user123", stage.UpdatedBy)
			}
		})
	}
}

func TestKeyModelRecord_CompleteStage(t *testing.T) {
	record, err := entities.NewKeyModelRecord(
		"CW001", "MAT001", "DESIGN", "Test Product",
		"LTN", "ISD001", "S25", "user123", nil, timePtr(time.Now()),
	)
	require.NoError(t, err)

	tests := []struct {
		name        string
		stageType   entities.StageType
		expectError bool
		errorMessage string
	}{
		{
			name:        "complete product creation stage",
			stageType:   entities.StageProductCreation,
			expectError: false,
		},
		{
			name:        "complete non-existent stage",
			stageType:   entities.StageCommercialEST,
			expectError: true,
			errorMessage: "stage not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := record.CompleteStage(tt.stageType, "user123")

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMessage)
			} else {
				assert.NoError(t, err)
				
				stage := record.GetStage(tt.stageType)
				assert.NotNil(t, stage)
				assert.Equal(t, entities.StatusCompleted, stage.Status)
				assert.NotNil(t, stage.CompletedAt)
				assert.Equal(t, "user123", stage.UpdatedBy)
			}
		})
	}
}

func TestKeyModelRecord_UpdateBasicInfo(t *testing.T) {
	record, err := entities.NewKeyModelRecord(
		"CW001", "MAT001", "DESIGN", "Test Product",
		"LTN", "ISD001", "S25", "user123", nil, nil,
	)
	require.NoError(t, err)

	tests := []struct {
		name         string
		colorwayId   string
		materialId   string
		keyModelType string
		productName  string
		fty          string
		isd          string
		season       string
		quantity     *int
		expectError  bool
	}{
		{
			name:         "valid update",
			colorwayId:   "CW002",
			materialId:   "MAT002",
			keyModelType: "PRODUCTION",
			productName:  "Updated Product",
			fty:          "VTN",
			isd:          "ISD002",
			season:       "F25",
			quantity:     intPtr(2000),
			expectError:  false,
		},
		{
			name:         "invalid update - empty colorway",
			colorwayId:   "",
			materialId:   "MAT002",
			keyModelType: "PRODUCTION",
			productName:  "Updated Product",
			fty:          "VTN",
			isd:          "ISD002",
			season:       "F25",
			expectError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := record.UpdateBasicInfo(
				tt.colorwayId,
				tt.materialId,
				tt.keyModelType,
				tt.productName,
				tt.fty,
				tt.isd,
				tt.season,
				"user123",
				tt.quantity,
			)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.colorwayId, record.GetColorwayId())
				assert.Equal(t, tt.materialId, record.GetMaterialId())
			}
		})
	}
}

func TestKeyModelRecord_ActivateDeactivate(t *testing.T) {
	record, err := entities.NewKeyModelRecord(
		"CW001", "MAT001", "DESIGN", "Test Product",
		"LTN", "ISD001", "S25", "user123", nil, nil,
	)
	require.NoError(t, err)

	// Initially active
	assert.True(t, record.IsActive())

	// Deactivate
	record.Deactivate("user123")
	assert.False(t, record.IsActive())

	// Activate
	record.Activate("user123")
	assert.True(t, record.IsActive())
}

// Helper functions
func intPtr(i int) *int {
	return &i
}

func timePtr(t time.Time) *time.Time {
	return &t
}

// Benchmark tests
func BenchmarkNewKeyModelRecord(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_, err := entities.NewKeyModelRecord(
			"CW001", "MAT001", "DESIGN", "Test Product",
			"LTN", "ISD001", "S25", "user123", nil, nil,
		)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkUpdateStageData(b *testing.B) {
	record, err := entities.NewKeyModelRecord(
		"CW001", "MAT001", "DESIGN", "Test Product",
		"LTN", "ISD001", "S25", "user123", nil, nil,
	)
	if err != nil {
		b.Fatal(err)
	}

	data := map[string]interface{}{
		"poReceived":   true,
		"pfcValidated": false,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err := record.UpdateStageData(entities.StageProductCreation, data, "user123")
		if err != nil {
			b.Fatal(err)
		}
	}
}
